
import os
from dotenv import load_dotenv
import sys
sys.path.append('../')

credential_list = []

load_dotenv()

class db_credentail:

    def db_credentail_handler(self):

        realm = os.environ.get("realm")

        if realm is not None:
            
            if realm in {'stiversag','carriageag','keerag','hwkia','jakesweeneyag','ginnmotorcompany','rossdowningauto'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_02")
                db_host = os.getenv("host")
                db_port = os.getenv("port_01")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm == 'kevinwhitaker':
                db_name = os.getenv("db_name_01")
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_01")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'fossmotors','firstteamag','chevyland','keycarsag','peninsubag','haloag','kunesbuickgmcgreenfield'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_02")
                db_host = os.getenv("host")
                db_port = os.getenv("port_02")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'corteseag','fitzgeraldautomall','gosschevrolet','kunescountryag','loveag','mullerag','missoulanissanhyundai'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_02")
                db_host = os.getenv("host")
                db_port = os.getenv("port_03")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'murgadoautomotivegroup','pattersonautogroup','haleyautomotivegroup','unitedchevroletbuickgmc','keystoneautogroup','brantmeier','carlockautogroup','demoenterprise','riverheadtoyota'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_04")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'kenwooddealergroup','barnescrossingautogroup','findlayautomotivegroup','randymarionautogroup','loyaltestag','matagabuickgmccadillac','mileoneautomotive','sheehyautostores','samlemanautomotivegroup'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_05")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'charliesmotormall','premierautomotive','deyarmanautomotivegroup','lawautomotivegroup','koonsofsilverspring','dicksmithautomotive','haselwoodautogroup','rkchevrolet','nationwidemotorsales'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_06")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'orangebuickgmc','piazzaautogroup','sunriseautogroup','shaverchryslerdodgejeepram','thurstonautocorporations','beachautogroup'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_07")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'alfaromeomaseratiofstpetersburg','reddingsubaru','charlieobaughautomotive','chastangford'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_09")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'dublinautogroup','dowlingford','stevewhiteautogroup','mainlineautomotivegroup'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_01")
                db_host = os.getenv("host")
                db_port = os.getenv("port_10")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'faithsautogroup','myrtlebeachchevrolet','raabefordlincoln','pageautogroupva','wondriesautogroup'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_02")
                db_host = os.getenv("host")
                db_port = os.getenv("port_11")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'fordofdalton'}:
                db_name = realm
                db_user = os.getenv("user_name")
                db_password = os.getenv("password_02")
                db_host = os.getenv("host")
                db_port = os.getenv("port_12")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            elif realm in {'demoenterprise_test','chevyland_test','corteseag_test','haloag_test','kunescountryag_test','haleyautomotivegroup_test','samlemanautomotivegroup_test','ginnmotorcompany_test','sheehyautostores_test','firstteamag_test','lawautomotivegroup_test','sampackag_test','demoenterprise_nada','sunriseautogroup_test','prime_dev_cdk','sampackag'}:
                db_name = realm
                db_user = os.getenv("user_01")
                db_password = os.getenv("password_03")
                db_host = os.getenv("host")
                db_port = os.getenv("port_08")

                credential_list.append({
                    "db_name": db_name,
                    "db_user": db_user,
                    "db_password": db_password,
                    "db_host": db_host,
                    "db_port": db_port
                })
                return credential_list
            

            
        else:
            print("realm is not set.")
