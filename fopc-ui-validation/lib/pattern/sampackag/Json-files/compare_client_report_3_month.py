import json
import csv
import re
import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>

def clean_number(value):
    """Remove formatting and convert to float if possible."""
    if value is None or value == "":
        return "Missing"
    if isinstance(value, str):
        value = re.sub(r'[$%,]', '', value)  # Remove $, %, and ,
        if re.match(r'\(.*\)', value):  # Check if value is in parentheses (negative number)
            value = "-" + value.strip("()")  # Convert (2) to -2
    try:
        return float(value)
    except ValueError:
        return value  # Return as is if not convertible

def extract_main_values(data):
    """Extract main KPI fields like Monthly FOPC, Total, ROI, etc., ensuring a dictionary structure."""
    keys_to_extract = ["Monthly FOPC", "Monthly DMS", "Total", "ROI", 
                       "Total Pts & Lbr GP Change", "Repair ELR Change"]
    
    main_values = {}
    for key in keys_to_extract:
        value = clean_number(data.get(key, "Missing"))
        main_values[key] = {  
            # "Second Month": value
            "3 MTH Avg (Baseline)":value
        }
    
    return main_values

def extract_kpis_from_dict(data):
    """Extract KPI values from the dictionary structure."""
    kpis = {}
    for kpi, values in data.get("KPIs", {}).items():
        if isinstance(values, list) and len(values) == 6:  # Handle list-based KPIs
            kpis[kpi] = {
                "3 MTH Avg (Baseline)": clean_number(values[0]),
                "Last Month": clean_number(values[1]),
                "Variance": clean_number(values[2]),
                "Prior Annual Pace": clean_number(values[3]),
                "Annual Pace":clean_number(values[4]),
                "Variance Annualized":clean_number(values[5])
            }
        elif isinstance(values, dict):  # Handle dictionary-based KPIs
            kpis[kpi] = {
                "3 MTH Avg (Baseline)":clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
                "Last Month":clean_number(values.get("Last Month", "Missing")),
                "Variance": clean_number(values.get("Variance", "Missing")),
                "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
                "Annual Pace":clean_number(values.get("Annual Pace", "Missing")),
                "Variance Annualized":clean_number(values.get("Variance Annualized", "Missing")),
                # "Second Month": clean_number(values.get("Second Month", "Missing")),
                # "First Month": clean_number(values.get("First Month", "Missing")),
                # "Variance": clean_number(values.get("Variance", "Missing"))
            }
    return kpis

def extract_category_kpis(data, category):
    """Extract KPIs from Competitive, Maintenance, and Repair sections."""
    kpis = {}
    category_data = data.get(category, {})
    for kpi, values in category_data.items():
        kpis[f"{category} - {kpi}"] = { 
            "3 MTH Avg (Baseline)":clean_number(values.get("3 MTH Avg (Baseline)", "Missing")),
            "Last Month":clean_number(values.get("Last Month", "Missing")),
            "Variance": clean_number(values.get("Variance", "Missing")),
            "Prior Annual Pace": clean_number(values.get("Prior Annual Pace", "Missing")),
            "Annual Pace":clean_number(values.get("Annual Pace", "Missing")),
            "Variance Annualized":clean_number(values.get("Variance Annualized", "Missing")),           
            # "Second Month": clean_number(values.get("Second Month", "Missing")),
            # "First Month": clean_number(values.get("First Month", "Missing")),
            # "Variance": clean_number(values.get("Variance", "Missing"))
        }
    return kpis

# Load data from JSON files
with open("Json-files/results_set_client_report_3_monthtest.json", "r") as file1:
    results_data = json.load(file1)

with open("Json-files/client_report_3month_ui.json", "r") as file2:
    ui_data = json.load(file2)

# Extract main KPI fields
main_values_first_file = extract_main_values(results_data)
main_values_second_file = extract_main_values(ui_data)

# Extract KPI values from "KPIs" section
kpis_first_file = extract_kpis_from_dict(results_data)
kpis_second_file = extract_kpis_from_dict(ui_data)

# Extract KPIs from Competitive, Maintenance, and Repair sections
for section in['COMPETITIVE', 'MAINTENANCE', 'REPAIR']:
    kpis_first_file.update(extract_category_kpis(results_data, section))
    kpis_second_file.update(extract_category_kpis(ui_data, section))


# Compare and store results
output_data = []
for kpi in set(kpis_first_file.keys()).union(set(kpis_second_file.keys())):
    first = kpis_first_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing", "Last Month": "Missing", "Variance": "Missing", "Prior Annual Pace":"Missing","Annual Pace":"Missing","Variance Annualized":"Missing"})
    second = kpis_second_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing", "Last Month": "Missing", "Variance": "Missing", "Prior Annual Pace":"Missing","Annual Pace":"Missing","Variance Annualized":"Missing"})
       # Compare all KPI values between both files
    is_match = all(
        first.get(metric, "Missing") == second.get(metric, "Missing")
        for metric in ["3 MTH Avg (Baseline)", "Last Month", "Variance", "Prior Annual Pace", "Annual Pace", "Variance Annualized"]
    )
    # Check if values match
    # is_match = first["3 MTH Avg (Baseline)"] == second["3 MTH Avg (Baseline)"] and second["Last Month"] == first["Last Month"] and first["Prior Annual Pace"]==second["Prior Annual Pace"] and first["Variance Annualized"]==second["Variance Annualized"] and first["Variance"]==second["Variance"]
    print(is_match,"is_match........")
    output_data.append([
        kpi, first["3 MTH Avg (Baseline)"], first["Last Month"], first["Variance"],first["Prior Annual Pace"],first["Annual Pace"],first["Variance Annualized"],
       second["3 MTH Avg (Baseline)"], second["Last Month"], second["Variance"],second["Prior Annual Pace"],second["Annual Pace"],second["Variance Annualized"], 
        is_match
    ])
for kpi in set(main_values_first_file.keys()).union(set(main_values_second_file.keys())):
    first_value = main_values_first_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]
    second_value = main_values_second_file.get(kpi, {"3 MTH Avg (Baseline)": "Missing"})["3 MTH Avg (Baseline)"]

    # Compare only "Second Month" values
    is_match = first_value == second_value

    output_data.append([
        kpi, first_value,'N/A','N/A','N/A',second_value, 'N/A','N/A','N/A',
        is_match
    ])

# Write results to CSV
output_csv = "comparison_result_3_month_client_report_test.csv"
with open(output_csv, "w", newline="", encoding="utf-8") as csvfile:
    writer = csv.writer(csvfile)
    writer.writerow(["KPI Name", "3 MTH Avg (Baseline) - File1", "Last Month - File1", "Variance - File1","Prior Annual Pace - File1","Annual Pace - File1","Variance Annualized- File1",
                     "3 MTH Avg (Baseline) - File2", "Last Month - File2", "Variance - File2","Prior Annual Pace - File2","Annual Pace - File2","Variance Annualized- File2" "Match Status"])
    writer.writerows(output_data)

print(f"Comparison results saved to {output_csv}")


# Read the CSV file and create an Excel workbook
csv_file = "comparison_result_3_month_client_report_test.csv"
xlsx_file = "comparison_client_report3_month_highlighted_test.xlsx"

# Convert CSV to Excel
wb = openpyxl.Workbook()
ws = wb.active
ws.title = "Comparison Results"

# Read CSV and populate Excel sheet
with open(csv_file, "r", encoding="utf-8") as file:
    reader = csv.reader(file)
    for row in reader:
        ws.append(row)

# Define yellow fill for highlighting
yellow_fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")

# Get column index of "Match (True/False)" (assumed to be in the last column)
match_column_index = ws.max_column

# Apply formatting to rows where "Match (True/False)" is False
for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=match_column_index, max_col=match_column_index):
    for cell in row:
        if cell.value == "False":  # Check if match is False
            for cell_to_fill in ws[cell.row]:  # Apply fill to the entire row
                cell_to_fill.fill = yellow_fill

# Save the formatted Excel file
wb.save(xlsx_file)
print(f"Excel file with highlighted mismatches saved as {xlsx_file}")

