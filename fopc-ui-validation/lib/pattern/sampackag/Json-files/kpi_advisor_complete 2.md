[{"KPI Single Advisor - Financial - Customer Pay": null, "Labor Sales / GP $ / GP %": "408970.0 / 303354 / 74.2% ", "Labor Sales Per RO / GP $ Per RO": "262.0 / 194.0", "Parts Sales / GP $ / GP %": "386334.0 / 123829 / 32.1%", "Parts Sales Per RO / GP $ Per RO": "247.0 / 79.0", "Labor & Parts Sales / Total GP $": "795304.0 / 427183.0", "Parts to Labor Ratio": "0.9 to $1", "B) Pricing - Customer Pay": null, "Repair Price Targets / Misses / % of Non-Compliance": "506 / 46 / 9%", "Parts Price Targets / Misses / % of Non-Compliance": "754 / 269 / 36%", "Competitive Hours / Sales / ELR": "738.3 / 35,158 / 48", "Maintenance Hours / Sales / ELR": "1,061.7 / 80,744 / 76", "Repair Hours / Sales / ELR": "1,698.7 / 293,068 / 173", "Total Hrs / Sales / ELR": "3,498.7 / 408,970 / 117", "What-If Repair / Total ELR Target Misses at 0%": "$172 / $111", "Maintenance / Repair Work Mix": "51% / 49%", "C) Volume": null, "CP / Wty / Int / All Vehicles": "1,562 / 1,056 / 442 / 3060", "Average Vehicles Per Day / % of Vehicles Serviced": "129 / 100%", "All ROs - Average Days Open": null, "Representing What % of Total": null, "CP & Wty - Avg Age / Miles Per Vehicle": "4.5 Years / 63,477 Miles", "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle": "7,724.8 / 325.3 / 2.2"}, {"D) Opportunities - CP Vehicles Under 60K Miles": null, "Total Count / % of Business": "777 / 50%", "1 Line Count / % Under 60K": "467 / 60%", "Labor / Parts / Total Sold Per 1 Line RO": "$43 / $69 / $112", "Labor / Parts / Total Sold Per Multi-Line RO": "$223 / $226 / $449", "Avg Jobs Per Multi-Line RO": 2.8, "G) Opportunities - CP Vehicles Over 60K Miles": null, "1 Line Count / % Over 60K": "338 / 43%"}, {"E) Opportunities - MPI (CP and Wty)": null, "Opportunities / Completed / %": "2,520 / 1,940 / 77%", "Upsell Potential $ / Sold $ / % Collected": "$572,238 / $516,619 / 90%", "Potential Hours / Sold Hours / % / Hours Sold Per MPI": "2,520 / 2,127.2 / 84% / 1.1"}, {"F) Opportunities - Menu Sales (CP and Wty)": null, "Menu Opportunities / Sold / %": "0 / 0 / 0%", "Upsell Potential $ / Sold $ / % Collected": "$0 / $0 / 0%", "Potential Hours / Sold Hours / % / Hours Sold Per Menu": "0 / 0 / 0% / 0.0"}, {"Goal": {"Labor Sales / GP $ / GP %": "74", "Parts Sales / GP $ / GP %": "35", "Repair Price Targets / Misses / % of Non-Compliance": "5", "Parts Price Targets / Misses / % of Non-Compliance": "10", "Maintenance / Repair Work Mix": "38", "All ROs - Average Days Open": "6", "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle": "2.2", "1 Line Count / % Under 60K": "20", "1 Line Count / % Over 60K": "35"}}, {"Variance": {"Labor Sales / GP $ / GP %": "0.2%", "Parts Sales / GP $ / GP %": "-2.9%", "Repair Price Targets / Misses / % of Non-Compliance": "4", "Parts Price Targets / Misses / % of Non-Compliance": "26", "Maintenance / Repair Work Mix": "13", "All ROs - Average Days Open": "0", "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle": "0.0", "1 Line Count / % Under 60K": "40%", "1 Line Count / % Over 60K": "8%"}}]