import math
import sys
sys.path.append('../')
from datetime import datetime
from collections import Counter
import pandas as pd
import os
import openpyxl
import json
import datetime
from dateutil.relativedelta import relativedelta
from decimal import Decimal, ROUND_HALF_UP
from db_handler.db_connector import getCustomerPayTypeGroupsList, allRevenueDetailsForClientReportCard3Month
# from db_handler.db_connector import  allRevenueDetailsForClientReportCard3Month
import math

def round_off(n, decimals=0):
    multiplier = Decimal(10) ** decimals
    if isinstance(n, float):
        n = Decimal(str(n))
    return float((n * multiplier).quantize(Decimal('1'), rounding=ROUND_HALF_UP) / multiplier)

retail_flag_DB_connect = getCustomerPayTypeGroupsList()
retail_flag = retail_flag_DB_connect.getCustomerPayTypeList()

from db_handler.db_connector import DbConnector, opcodePayTypeFixedRateStatus, payTypeFixedRates, opcodeFixedRates, gridData, opcodeTable


#Get the required environment variables
store_id = os.environ.get('store_id')
realm = os.environ.get('realm') 
dms_fees = Decimal(os.environ.get('dms_fees', "0"))
# dms_fees = Decimal(os.environ.get('dms_fees'))
fopc_fees = Decimal(os.environ.get('fopc_fees',"0"))

s_date_env = os.environ.get('start_date')
e_date_env = os.environ.get('end_date')
# required_date_range = pd.date_range(start=s_date_env, end=e_date_env, freq='MS').strftime('%Y-%m').tolist()
# Convert to pandas Timestamp (ensures proper handling of date formats)
s_date = pd.Timestamp(s_date_env)
e_date = pd.Timestamp(e_date_env)
# if not s_date_env or not e_date_env:
#     raise ValueError("Start and End dates must be valid non-empty strings.")

# Generate date range with 'MS' (Month Start)
# required_date_range = pd.date_range(start=s_date, end=e_date, freq='MS').strftime('%Y-%m').tolist()
# required_date_range=[ '2024-07', '2024-08', '2024-09']

# print(required_date_range)
required_date_range=['']
# last_month = os.environ.get('last_month')
# last_month='2024-09'
# last_month_date = datetime.strptime(last_month, "%Y-%m")
# last_month_date=
# date_ranges = [(last_month_date - relativedelta(months=i)).strftime("%Y-%m") for i in range(3)]
# date_ranges= ['2023-10','2023-09','2023-08']
required_date_range = ['2023-07', '2023-08', '2023-09']

last_month = '2024-10'

date_ranges = ['2024-10', '2024-09', '2024-08']


all_revenue_details = pd.read_csv('F:\\Sinju_work\\fopc-ui-validation\\fopc-ui-validation\\lib\\pattern\\sampackag\\raw-data\\all_revenue_details.csv', na_values=[], keep_default_na=False)
paytype_retail_flag_setting = pd.read_csv('F:\\Sinju_work\\fopc-ui-validation\\fopc-ui-validation\\lib\\pattern\\sampackag\\raw-data\\paytype_retail_flag_setting.csv', na_values=[],keep_default_na=False)
all_revenue_details_df = all_revenue_details
# print("Fetching data from DB......")
# all_revenue_details_table_db_connect = allRevenueDetailsForClientReportCard3Month()
# all_revenue_details_df = all_revenue_details_table_db_connect.getTableResult()
# print("Data load completed ......")
# # all_revenue_details_df.to_csv('../Output/all_revenue_details.csv')
# # retail_flag = {'C', 'M','E'}
# # all_revenue_details_df = pd.read_csv('F:\\Sinju_work\\fopc-ui-validation\\fopc-ui-validation\\lib\\pattern\\sampackag\\raw-data\\all_revenue_details.csv', na_values=[], keep_default_na=False)
# paytype_retail_flag_setting = pd.read_csv('F:\\Sinju_work\\fopc-ui-validation\\fopc-ui-validation\\lib\\pattern\\sampackag\\raw-data\\paytype_retail_flag_setting.csv', na_values=[],
                                        #    keep_default_na=False)
                                           
# all_revenue_details = pd.read_csv('raw-data/all_revenue_details.csv', na_values=[], keep_default_na=False)
# paytype_retail_flag_setting = pd.read_csv('raw-data/paytype_retail_flag_setting.csv', na_values=[], keep_default_na=False)
# Initializing new data frame to filter only required advisor and technician
filtered_df = all_revenue_details_df[
    (all_revenue_details_df['department'] == 'Service') & 
    (all_revenue_details_df['opcategory'] != 'N/A') & 
    # (all_revenue_details_df['opcategory'] != 'SHOP SUPPLIES') &
    (all_revenue_details_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
    # (all_revenue_details_df['hide_ro'].astype(int) != 1)
    (all_revenue_details_df['hide_ro'] != True)
& (all_revenue_details_df['store_id'].astype(str).str.strip() == '254322619')
    ]
# filtered_df =all_revenue_details_df[
#     (all_revenue_details_df['department'] == 'Service') & 
#     (all_revenue_details_df['opcategory'] != 'N/A') & 
#     # (all_revenue_details_df['opcategory'] != 'SHOP SUPPLIES') &
#     (all_revenue_details_df['opcategory'].isin(['REPAIR','COMPETITIVE','MAINTENANCE'])) &
#     # (all_revenue_details_df['hide_ro'].astype(int) != 1)
#     (all_revenue_details_df['hide_ro'] != True)
# & (all_revenue_details_df['store_id'].astype(str).str.strip() == '254322619')
#     ]
# paytype_retail_flag_setting['store_id'] = paytype_retail_flag_setting['store_id'].astype(str).str.strip()
# filtered_df.to_csv('../Output/filtered_df123.csv')
# print(filtered_df,"filtered_df..........................")
merged_df = filtered_df.merge(
    paytype_retail_flag_setting, 
    left_on=['paytypegroup', 'store_id'], 
    right_on=['source_paytype', 'store_id'], 
    how='left'
)
# print(merged_df,"merged_df........")
# merged_df.to_csv('../Output/filtered_df123m.csv')
#filtered_df = filtered_df[filtered_df['department'] == 'Service']

merged_df = merged_df.copy()  # Create a deep copy of filtered_df to avoid the warning
# all_revenue_details_df.to_csv('../Output/all_revenue_details_df123.csv')
#all_revenue_details_df = all_revenue_details_df[all_revenue_details_df['department'] == 'Service']

# all_revenue_details_df = all_revenue_details_df.copy()  # Create a deep copy of all_revenue_details_df to avoid the warning
# RO number with different closeddate will be considered as different RO, joining RO number and closeddate to find out the unique RO number
# all_revenue_details_df['unique_ro_number'] = all_revenue_details_df['ronumber'].astype(str) + '_' + all_revenue_details_df['closeddate'].astype(str)
merged_df['unique_ro_number'] = merged_df['ronumber'].astype(str) + '_' + merged_df['closeddate'].astype(str)
retail_flag = {'C', 'M','E'}
# Define customer and warranty pay types dynamically
if 'C' in retail_flag and not 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C'}
    warranty_pay_types = {'W', 'F', 'M', 'E'}
elif 'C' in retail_flag and not 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'M'}
    warranty_pay_types = {'W', 'F', 'E'}
elif 'C' in retail_flag and 'E' in retail_flag and not 'M' in retail_flag:
    customer_pay_types = {'C', 'E'}
    warranty_pay_types = {'W', 'F', 'M'}
elif 'C' in retail_flag and 'E' in retail_flag and 'M' in retail_flag:
    customer_pay_types = {'C', 'E', 'M'}
    warranty_pay_types = {'W', 'F'}

total_CP_revenue_details_df = merged_df[merged_df['paytypegroup'].isin(customer_pay_types)]
# Coverting it to data frame
#total_CP_revenue_details_df = pd.DataFrame(list_of_paytypegroup_C)
#total_CP_revenue_details_df.to_csv('../Output/Paytype_C_list.csv')


total_CP_revenue_details_df = total_CP_revenue_details_df.copy()

# Use .loc to set the values
total_CP_revenue_details_df.loc[:, 'lbrsale'] = pd.to_numeric(total_CP_revenue_details_df['lbrsale'].fillna(0), errors='coerce')
total_CP_revenue_details_df.loc[:, 'lbrsoldhours'] = pd.to_numeric(total_CP_revenue_details_df['lbrsoldhours'].fillna(0), errors='coerce')
total_CP_revenue_details_df.loc[:, 'prtextendedsale'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedsale'].fillna(0), errors='coerce')
total_CP_revenue_details_df.loc[:, 'prtextendedcost'] = pd.to_numeric(total_CP_revenue_details_df['prtextendedcost'].fillna(0), errors='coerce')

total_CP_revenue_details_df = total_CP_revenue_details_df[
~((total_CP_revenue_details_df['lbrsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['lbrsoldhours'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedsale'].fillna(0) == 0) &
    (total_CP_revenue_details_df['prtextendedcost'].fillna(0) == 0))
]

# print(total_CP_revenue_details_df,"total_CP_revenue_details_df...")
total_CP_revenue_details_df.to_csv('../Output/Total_Revenue_Details.csv')

total_CP_revenue_L_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'] == last_month]
#total_CP_revenue_L_month.to_csv("../Output/total_CP_revenue_L_month.csv")
total_CP_revenue_L_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(date_ranges)]
#total_CP_revenue_L_3_month.to_csv("../Output/total_CP_revenue_L_3_month.csv")
total_CP_revenue_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(required_date_range)]


# total_CP_revenue_L_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'] == last_month]
# # total_CP_revenue_L_month.to_csv("../Output/total_CP_revenue_L_month.csv")
# total_CP_revenue_L_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(date_ranges)]
# # total_CP_revenue_L_3_month.to_csv("../Output/total_CP_revenue_L_3_month.csv")
# # print( total_CP_revenue_details_df[total_CP_revenue_details_df['month_year']]," total_CP_revenue_details_df[total_CP_revenue_details_df['month_year']..........")
# total_CP_revenue_3_month = total_CP_revenue_details_df[total_CP_revenue_details_df['month_year'].isin(required_date_range)]
# total_CP_revenue_3_month.to_csv("../Output/total_CP_revenue_3_month.csv")
Client_Report_Card = []
# RO Count calculation
RO_count_3_month_avg_value = 0
RO_count_3_month_avg = 0
RO_count_prior_annual_pace = 0
Sold_hours_3_month_avg_value = 0
Sold_hours_3_month_avg = 0
Sold_hours_prior_annual_pace = 0
# Parts_sale_L_month=0
# Parts_GP_prior_annual_pace=0
# Parts_sale_3_month_avg=0
# Parts_sale_prior_annual_pace=0
# Parts_GP_L_month=0
# Parts_GP_3_month_avg=0
# Labor_Parts_sale_3_month_avg=0
# Labor_Parts_sale_prior_annual_pace=0
# Labor_Parts_GP_3_month_avg=0
# Labor_Parts_GP_prior_annual_pace=0
# Total_ELR_prior_annual_pace=0
if not total_CP_revenue_3_month.empty:
    #RO Count calculation
   #RO Count calculation
    RO_count_3_month = total_CP_revenue_3_month['unique_ro_number'].nunique()
    print(RO_count_3_month,"RO_count_3_month...")
    RO_count_3_month_avg_value = RO_count_3_month / 3
    RO_count_3_month_avg = round_off(RO_count_3_month_avg_value)
    RO_count_prior_annual_pace_value = RO_count_3_month_avg_value * 12
    RO_count_prior_annual_pace = round_off(RO_count_prior_annual_pace_value)
    # Sold Hours Calculations
      # Sold Hours Calculations
    Sold_hours_3_month = total_CP_revenue_3_month[(total_CP_revenue_3_month['opcategory'] != 'N/A') & 
                                (total_CP_revenue_3_month['opcategory'] != 'SHOP SUPPLIES')]['lbrsoldhours'].sum()
    # Sold_hous_3_month = total_CP_revenue_3_month['lbrsoldhours'].sum()
    print(Sold_hours_3_month,"Sold_hous_3_month.........")
    Sold_hours_3_month_avg_value = Sold_hours_3_month / 3
    Sold_hours_3_month_avg = round_off(Sold_hours_3_month_avg_value)
    Sold_hours_prior_annual_pace_value = Sold_hours_3_month_avg_value * 12
    Sold_hours_prior_annual_pace = round_off(Sold_hours_prior_annual_pace_value)
    #Hours Per RO calculations
    
    if RO_count_3_month != 0:
        Hours_per_RO_3_month_avg_value = Sold_hours_3_month / RO_count_3_month
        Hours_per_RO_3_month_avg = round_off(Hours_per_RO_3_month_avg_value)


    if RO_count_prior_annual_pace_value != 0:
        Hours_per_RO_prior_annual_pace_value = Sold_hours_prior_annual_pace_value / RO_count_prior_annual_pace_value
        Hours_per_RO_prior_annual_pace = round_off(Hours_per_RO_prior_annual_pace_value)
    # ELR calculation
    Labor_sale_value_3_month = float(total_CP_revenue_3_month['lbrsale'].sum())
    
    if Sold_hours_3_month != 0:
        Total_ELR_3_month_value = Labor_sale_value_3_month / Sold_hours_3_month
        Total_ELR_3_month = round_off(Total_ELR_3_month_value, 2)
    Total_ELR_prior_annual_pace = Total_ELR_3_month
    total_CP_revenue_3_month_REP = total_CP_revenue_3_month[total_CP_revenue_3_month['opcategory'] == 'REPAIR']
    
    if not total_CP_revenue_3_month_REP.empty:
        total_CP_revenue_3_month_REP_sale = total_CP_revenue_3_month_REP['lbrsale'].sum()
        total_CP_revenue_3_month_REP_hours = total_CP_revenue_3_month_REP['lbrsoldhours'].sum()
    
    
    if total_CP_revenue_3_month_REP_hours != 0:
        Repair_ELR_3_month_avg_value = total_CP_revenue_3_month_REP_sale / total_CP_revenue_3_month_REP_hours
        Repair_ELR_3_month_avg = round_off(Repair_ELR_3_month_avg_value, 2)
        Repair_ELR_prior_annual_pace_value = Repair_ELR_3_month_avg_value
        Repair_ELR_prior_annual_pace = Repair_ELR_3_month_avg
    #Labor Gross Profit %
    Labor_sale_value_3_month = float(total_CP_revenue_3_month['lbrsale'].sum())
    Labor_cost_value_3_month = float(total_CP_revenue_3_month['lbrcost'].sum())
    
    if Labor_sale_value_3_month != 0:
        Labor_GP_perc_3_month_avg_value = ((Labor_sale_value_3_month - Labor_cost_value_3_month) / Labor_sale_value_3_month) * 100
        Labor_GP_perc_3_month_avg = round_off(Labor_GP_perc_3_month_avg_value)
        Labor_GP_perc_prior_annual_pace = Labor_GP_perc_3_month_avg
    #Parts Gross Profit %
    Parts_sale_value_3_month = float(total_CP_revenue_3_month['prtextendedsale'].sum())
    Parts_cost_value_3_month = float(total_CP_revenue_3_month['prtextendedcost'].sum())
    
    
    if Parts_sale_value_3_month != 0:
        Parts_GP_perc_3_month_avg_value = ((Parts_sale_value_3_month - Parts_cost_value_3_month) / Parts_sale_value_3_month) * 100
        Parts_GP_perc_3_month_avg = round_off(Parts_GP_perc_3_month_avg_value)
        Parts_GP_perc_prior_annual_pace_value = Parts_GP_perc_3_month_avg_value
        Parts_GP_perc_prior_annual_pace = Parts_GP_perc_3_month_avg
        
    #Labor Sale
    Labor_sale_3_month_avg_value = Labor_sale_value_3_month / 3
    Labor_sale_3_month_avg = round_off(Labor_sale_3_month_avg_value)
    Labor_sale_prior_annual_pace_value = Labor_sale_3_month_avg_value * 12
    Labor_sale_prior_annual_pace = round_off(Labor_sale_prior_annual_pace_value)
    #Labor GP
    Labor_GP_3_month_avg_value = (Labor_sale_value_3_month - Labor_cost_value_3_month) / 3
    Labor_GP_3_month_avg = round_off(Labor_GP_3_month_avg_value)
    Labor_GP_prior_annual_pace_value = Labor_GP_3_month_avg_value * 12
    Labor_GP_prior_annual_pace = round_off(Labor_GP_prior_annual_pace_value)
    #Parts Sale
    Parts_sale_3_month_avg_value = Parts_sale_value_3_month / 3
    Parts_sale_3_month_avg = round_off(Parts_sale_3_month_avg_value)
    Parts_sale_prior_annual_pace_value = Parts_sale_3_month_avg_value * 12
    Parts_sale_prior_annual_pace = round_off(Parts_sale_prior_annual_pace_value)
    #Labor GP
    Parts_GP_3_month_avg_value = (Parts_sale_value_3_month - Parts_cost_value_3_month) / 3
    Parts_GP_3_month_avg = round_off(Parts_GP_3_month_avg_value)
    Parts_GP_prior_annual_pace_value = Parts_GP_3_month_avg_value * 12
    Parts_GP_prior_annual_pace = round_off(Parts_GP_prior_annual_pace_value)
    #Total Labor & Parts Sale
    Labor_Parts_sale_3_month_avg_value = (Labor_sale_value_3_month + Parts_sale_value_3_month) / 3
    Labor_Parts_sale_3_month_avg = round_off(Labor_Parts_sale_3_month_avg_value)
    Labor_Parts_sale_prior_annual_pace_value = Labor_Parts_sale_3_month_avg_value * 12
    Labor_Parts_sale_prior_annual_pace = round_off(Labor_Parts_sale_prior_annual_pace_value)
    #Total Labor & Parts GP
    Labor_Parts_GP_3_month_avg_value = ((Labor_sale_value_3_month + Parts_sale_value_3_month) - (Labor_cost_value_3_month + Parts_cost_value_3_month)) / 3
    Labor_Parts_GP_3_month_avg = round_off(Labor_Parts_GP_3_month_avg_value)
    Labor_Parts_GP_prior_annual_pace_value = Labor_Parts_GP_3_month_avg_value * 12
    Labor_Parts_GP_prior_annual_pace = round_off(Labor_Parts_GP_prior_annual_pace_value)

    

RO_count_L_month = 0
Sold_hours_L_month_value = 0
Sold_hours_L_month = 0
Hours_per_RO_L_month_value = 0
Hours_per_RO_L_month = 0
Total_ELR_L_month_value = 0
Total_ELR_L_month = 0
Repair_ELR_L_month_value = 0
Repair_ELR_L_month = 0
total_CP_revenue_L_month_REP_sale = 0
total_CP_revenue_L_month_REP_hours = 0
Labor_GP_perc_L_month_value = 0
Labor_GP_perc_L_month = 0
# Parts_GP_perc_L_month_value = 0
# Parts_GP_perc_L_month = 0
# Labor_sale_L_month=0
# Labor_GP_L_month=0
# Labor_Parts_sale_L_month=0
# Labor_Parts_GP_L_month=0
if not total_CP_revenue_L_month.empty:
    # RO Count calculation
    RO_count_L_month = total_CP_revenue_L_month['unique_ro_number'].nunique()
    # Sold Hours Calculations
    # [
                                # (total_CP_revenue_L_month['opcategory'] != 'N/A') & 
                                # (total_CP_revenue_L_month['opcategory'] != 'SHOP SUPPLIES')
                                # ]
    Sold_hours_L_month_value = total_CP_revenue_L_month['lbrsoldhours'].sum()
    Sold_hours_L_month = round_off(Sold_hours_L_month_value)
    #Hours per RO calculation
   
    if RO_count_L_month != 0:
        Hours_per_RO_L_month_value = Sold_hours_L_month_value / RO_count_L_month
        Hours_per_RO_L_month = round_off(Hours_per_RO_L_month_value)
    # ELR calculation
    Labor_sale_value_L_month = float(total_CP_revenue_L_month['lbrsale'].sum())
   
    if Sold_hours_L_month != 0:
        Total_ELR_L_month_value = Labor_sale_value_L_month / Sold_hours_L_month_value
        Total_ELR_L_month = round_off(Total_ELR_L_month_value, 2)
    total_CP_revenue_L_month_REP = total_CP_revenue_L_month[total_CP_revenue_L_month['opcategory'] == 'REPAIR']
    
    if not total_CP_revenue_L_month_REP.empty:
        total_CP_revenue_L_month_REP_sale = total_CP_revenue_L_month_REP['lbrsale'].sum()
        total_CP_revenue_L_month_REP_hours = total_CP_revenue_L_month_REP['lbrsoldhours'].sum()
    
   
    if total_CP_revenue_L_month_REP_hours != 0:
        Repair_ELR_L_month_value = total_CP_revenue_L_month_REP_sale / total_CP_revenue_L_month_REP_hours
        Repair_ELR_L_month = round_off(Repair_ELR_L_month_value, 2)
    #Labor Gross Profit %
    Labor_sale_value_L_month = float(total_CP_revenue_L_month['lbrsale'].sum())
    Labor_cost_value_L_month = float(total_CP_revenue_L_month['lbrcost'].sum())
    
    if Labor_sale_value_L_month != 0:
        Labor_GP_perc_L_month_value = ((Labor_sale_value_L_month - Labor_cost_value_L_month) / Labor_sale_value_L_month) * 100
        Labor_GP_perc_L_month = round_off(Labor_GP_perc_L_month_value)
        Labor_GP_perc_prior_annual_pace_value = Labor_GP_perc_L_month_value
    #Parts Gross Profit %
    Parts_sale_value_L_month = float(total_CP_revenue_L_month['prtextendedsale'].sum())
    Parts_cost_value_L_month = float(total_CP_revenue_L_month['prtextendedcost'].sum())
    
    if Parts_sale_value_L_month != 0:
        Parts_GP_perc_L_month_value = ((Parts_sale_value_L_month - Parts_cost_value_L_month) / Parts_sale_value_L_month) * 100
        Parts_GP_perc_L_month = round_off(Parts_GP_perc_L_month_value)
        #Parts_GP_perc_prior_annual_pace_value = Parts_GP_perc_L_month_value
    #Labor Sale
    Labor_sale_L_month_value = Labor_sale_value_L_month
    Labor_sale_L_month = round_off(Labor_sale_L_month_value)
    #Labor GP
    Labor_GP_L_month_value = Labor_sale_value_L_month - Labor_cost_value_L_month
    Labor_GP_L_month = round_off(Labor_GP_L_month_value)
    #Parts Sale
    Parts_sale_L_month_value = Parts_sale_value_L_month
    Parts_sale_L_month = round_off(Parts_sale_L_month_value)
    #Labor GP
    Parts_GP_L_month_value = Parts_sale_value_L_month - Parts_cost_value_L_month
    Parts_GP_L_month = round_off(Parts_GP_L_month_value)
    #Total Labor & Parts Sale
    Labor_Parts_sale_L_month_value = Labor_sale_value_L_month + Parts_sale_value_L_month
    Labor_Parts_sale_L_month = round_off(Labor_Parts_sale_L_month_value)
    print(Labor_Parts_sale_L_month,"Labor_Parts_sale_L_month.............")
    #Total Labor & Parts GP
    Labor_Parts_GP_L_month_value = (Labor_sale_value_L_month + Parts_sale_value_L_month) - (Labor_cost_value_L_month + Parts_cost_value_L_month)
    Labor_Parts_GP_L_month = round_off(Labor_Parts_GP_L_month_value)
    

RO_count_current_annual_pace = 0
RO_count_L_3_month_avg_value = 0
RO_count_L_3_month_avg = 0
Sold_hours_L_3_month_avg_value = 0
Sold_hours_L_3_month_avg = 0
RO_count_current_annual_pace_value = 0
Sold_hours_current_annual_pace = 0

# Labor_Parts_sale_L_month=0
if not total_CP_revenue_L_3_month.empty: 
    #RO Count calculation
    RO_count_L_3_month = total_CP_revenue_L_3_month['unique_ro_number'].nunique()
    RO_count_L_3_month_avg_value = RO_count_L_3_month / 3
    RO_count_L_3_month_avg = round_off(RO_count_L_3_month_avg_value)
    RO_count_current_annual_pace_value = RO_count_L_3_month_avg_value * 12
    RO_count_current_annual_pace = round_off(RO_count_current_annual_pace_value)
    # Sold Hours calculations
    # [(total_CP_revenue_L_3_month['opcategory'] != 'N/A') & 
    #                             (total_CP_revenue_L_3_month['opcategory'] != 'SHOP SUPPLIES')]
    Sold_hours_L_3_month = total_CP_revenue_L_3_month['lbrsoldhours'].sum()
    
    Sold_hours_L_3_month_avg_value = Sold_hours_L_3_month / 3
    Sold_hours_L_3_month_avg = round_off(Sold_hours_L_3_month_avg_value)
    Sold_hours_current_annual_pace = round_off(Sold_hours_L_3_month_avg_value * 12)
    # Hours per RO calculation
    
    if RO_count_L_3_month != 0:
        Hours_per_RO_current_annual_pace_value = Sold_hours_L_3_month / RO_count_L_3_month
        Hours_per_RO_current_annual_pace = round_off(Hours_per_RO_current_annual_pace_value)
    # ELR calculation
    Labor_sale_value_L_3_month = float(total_CP_revenue_L_3_month['lbrsale'].sum())
    
    if Sold_hours_L_3_month != 0:
        Total_ELR_L_3_month_value = Labor_sale_value_L_3_month / Sold_hours_L_3_month
        #Total_ELR_L_3_month = round_off(Total_ELR_L_3_month_value)
        Total_ELR_L_3_month_avg = round_off(Total_ELR_L_3_month_value, 2)
    Total_ELR_current_annual_pace = Total_ELR_L_3_month_avg
    total_CP_revenue_L_3_month_REP = total_CP_revenue_L_3_month[total_CP_revenue_L_3_month['opcategory'] == 'REPAIR']
    
    if not total_CP_revenue_L_3_month_REP.empty:
        total_CP_revenue_L_3_month_REP_sale = total_CP_revenue_L_3_month_REP['lbrsale'].sum()
        total_CP_revenue_L_3_month_REP_hours = total_CP_revenue_L_3_month_REP['lbrsoldhours'].sum()
    

    
    
    if total_CP_revenue_L_3_month_REP_hours != 0:
        Repair_ELR_L_3_month_avg_value = total_CP_revenue_L_3_month_REP_sale / total_CP_revenue_L_3_month_REP_hours
        Repair_ELR_L_3_month_avg = round_off(Repair_ELR_L_3_month_avg_value, 2)
        Repair_ELR_current_annual_pace_value = Repair_ELR_L_3_month_avg_value
        Repair_ELR_current_annual_pace = Repair_ELR_L_3_month_avg
    #Labor Gross Profit %
    Labor_sale_value_L_3_month = float(total_CP_revenue_L_3_month['lbrsale'].sum())
    Labor_cost_value_L_3_month = float(total_CP_revenue_L_3_month['lbrcost'].sum())
    
    if Labor_sale_value_L_3_month != 0:
        Labor_GP_perc_L_3_month_avg_value = ((Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / Labor_sale_value_L_3_month) * 100
        Labor_GP_perc_L_3_month_avg = round_off(Labor_GP_perc_L_3_month_avg_value)
        Labor_GP_perc_current_annual_pace_value = Labor_GP_perc_L_3_month_avg_value
        Labor_GP_perc_current_annual_pace = Labor_GP_perc_L_3_month_avg
    #Parts Gross Profit %
    Parts_sale_value_L_3_month = float(total_CP_revenue_L_3_month['prtextendedsale'].sum())
    Parts_cost_value_L_3_month = float(total_CP_revenue_L_3_month['prtextendedcost'].sum())
    
    
    if Parts_sale_value_L_3_month != 0:
        Parts_GP_perc_L_3_month_avg_value = ((Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / Parts_sale_value_L_3_month) * 100
        Parts_GP_perc_L_3_month_avg = round_off(Parts_GP_perc_L_3_month_avg_value)
        Parts_GP_perc_current_annual_pace_value = Parts_GP_perc_L_3_month_avg_value
        Parts_GP_perc_current_annual_pace = Parts_GP_perc_L_3_month_avg
        
    #Labor Sale
    Labor_sale_L_3_month_avg_value = Labor_sale_value_L_3_month / 3
    Labor_sale_L_3_month_avg = round_off(Labor_sale_L_3_month_avg_value)
    Labor_sale_current_annual_pace_value = Labor_sale_L_3_month_avg_value * 12
    Labor_sale_current_annual_pace = round_off(Labor_sale_current_annual_pace_value)
    #Labor GP
    Labor_GP_L_3_month_avg_value = (Labor_sale_value_L_3_month - Labor_cost_value_L_3_month) / 3
    Labor_GP_L_3_month_avg = round_off(Labor_GP_L_3_month_avg_value)
    Labor_GP_current_annual_pace_value = Labor_GP_L_3_month_avg_value * 12
    Labor_GP_current_annual_pace = round_off(Labor_GP_current_annual_pace_value)
    #Parts Sale
    Parts_sale_L_3_month_avg_value = Parts_sale_value_L_3_month / 3
    Parts_sale_L_3_month_avg = round_off(Parts_sale_L_3_month_avg_value)
    Parts_sale_current_annual_pace_value = Parts_sale_L_3_month_avg_value * 12
    Parts_sale_current_annual_pace = round_off(Parts_sale_current_annual_pace_value)
    #Labor GP
    Parts_GP_L_3_month_avg_value = (Parts_sale_value_L_3_month - Parts_cost_value_L_3_month) / 3
    Parts_GP_L_3_month_avg = round_off(Parts_GP_L_3_month_avg_value)
    Parts_GP_current_annual_pace_value = Parts_GP_L_3_month_avg_value * 12
    Parts_GP_current_annual_pace = round_off(Parts_GP_current_annual_pace_value)
    #Total Labor & Parts Sale
    Labor_Parts_sale_L_3_month_avg_value = (Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) / 3
    Labor_Parts_sale_L_3_month_avg = round_off(Labor_Parts_sale_L_3_month_avg_value)
    Labor_Parts_sale_current_annual_pace_value = Labor_Parts_sale_L_3_month_avg_value * 12
    Labor_Parts_sale_current_annual_pace = round_off(Labor_Parts_sale_current_annual_pace_value)
    #Total Labor & Parts GP
    Labor_Parts_GP_L_3_month_avg_value = ((Labor_sale_value_L_3_month + Parts_sale_value_L_3_month) - (Labor_cost_value_L_3_month + Parts_cost_value_L_3_month)) / 3
    Labor_Parts_GP_L_3_month_avg = round_off(Labor_Parts_GP_L_3_month_avg_value)
    Labor_Parts_GP_current_annual_pace_value = Labor_Parts_GP_L_3_month_avg_value * 12
    Labor_Parts_GP_current_annual_pace = round_off(Labor_Parts_GP_current_annual_pace_value)

RO_count_1_month_variance = RO_count_L_month - RO_count_3_month_avg
RO_count_annual_variance = RO_count_current_annual_pace - RO_count_prior_annual_pace
Sold_hours_1_month_variance = round_off(Sold_hours_L_month - Sold_hours_3_month_avg)
Sold_hours_annual_variance = round_off(Sold_hours_current_annual_pace - Sold_hours_prior_annual_pace)
Hours_per_RO_1_month_variance = round_off(Hours_per_RO_L_month - Hours_per_RO_3_month_avg)
Hours_per_RO_annual_variance = round_off(Hours_per_RO_current_annual_pace - Hours_per_RO_prior_annual_pace)
Total_ELR_1_month_variance_value = Total_ELR_L_month - Total_ELR_3_month
Total_ELR_1_month_variance = round_off(Total_ELR_1_month_variance_value, 2)
Total_ELR_annual_variance_value = Total_ELR_L_3_month_avg - Total_ELR_3_month
Total_ELR_annual_variance = round_off(Total_ELR_annual_variance_value, 2)
Repair_ELR_1_month_variance_value = Repair_ELR_L_month - Repair_ELR_3_month_avg
Repair_ELR_1_month_variance = Repair_ELR_1_month_variance_value
Repair_ELR_annual_variance_value = Repair_ELR_L_3_month_avg - Repair_ELR_3_month_avg
Repair_ELR_annual_variance = round_off(Repair_ELR_annual_variance_value, 2)
Labor_GP_perc_1_month_variance = round_off(Labor_GP_perc_L_month - Labor_GP_perc_3_month_avg)
Labor_GP_perc_annual_variance = round_off(Labor_GP_perc_current_annual_pace - Labor_GP_perc_prior_annual_pace)
Parts_GP_perc_1_month_variance = round_off(Parts_GP_perc_L_month - Parts_GP_perc_3_month_avg)
Parts_GP_perc_annual_variance = round_off(Parts_GP_perc_current_annual_pace - Parts_GP_perc_prior_annual_pace)
Labor_sale_1_month_variance = round_off(Labor_sale_L_month - Labor_sale_3_month_avg)
Labor_sale_annual_variance = round_off(Labor_sale_current_annual_pace - Labor_sale_prior_annual_pace)
Labor_GP_1_month_variance = round_off(Labor_GP_L_month - Labor_GP_3_month_avg)
Labor_GP_annual_variance = round_off(Labor_GP_current_annual_pace - Labor_GP_prior_annual_pace)
Parts_sale_1_month_variance = round_off(Parts_sale_L_month - Parts_sale_3_month_avg)
Parts_sale_annual_variance = round_off(Parts_sale_current_annual_pace - Parts_sale_prior_annual_pace)
Parts_GP_1_month_variance = round_off(Parts_GP_L_month - Parts_GP_3_month_avg)
Parts_GP_annual_variance = round_off(Parts_GP_current_annual_pace - Parts_GP_prior_annual_pace)

Labor_Parts_sale_1_month_variance = round_off(Labor_Parts_sale_L_month - Labor_Parts_sale_3_month_avg)
Labor_Parts_sale_annual_variance = round_off(Labor_Parts_sale_current_annual_pace - Labor_Parts_sale_prior_annual_pace)
Labor_Parts_GP_1_month_variance_value = Labor_Parts_GP_L_month - Labor_Parts_GP_3_month_avg
Labor_Parts_GP_1_month_variance = round_off(Labor_Parts_GP_1_month_variance_value)
Labor_Parts_GP_annual_variance_value = Labor_Parts_GP_current_annual_pace - Labor_Parts_GP_prior_annual_pace
Labor_Parts_GP_annual_variance = round_off(Labor_Parts_GP_annual_variance_value)

#ROI Calculation based on Total Parts & Labor GP variance
Monthly_fees = float(dms_fees + fopc_fees)
Lbr_Pts_GP_1_month_ROI = 0
if float(Monthly_fees) != 0:
    Lbr_Pts_GP_1_month_ROI = round_off(((float(Labor_Parts_GP_1_month_variance) - float(Monthly_fees)) / float(Monthly_fees)) * 100)

Sold_hour_L_month_REP = round_off(total_CP_revenue_L_month[total_CP_revenue_L_month['opcategory'] == 'REPAIR']['lbrsoldhours'].sum())
Rep_ELR_1_month_ROI = 0
if float(Monthly_fees) != 0:
    Rep_ELR_1_month_ROI = round_off((round_off((float(Repair_ELR_1_month_variance) * float(Sold_hour_L_month_REP)) - float(Monthly_fees)) / float(Monthly_fees)) * 100)

Lbr_Pts_GP_annual_ROI = 0
if float(Monthly_fees) != 0:
    Lbr_Pts_GP_annual_ROI = round_off(((float(Labor_Parts_GP_annual_variance) - (float(Monthly_fees) * 12)) / (float(Monthly_fees) * 12)) * 100)
    
    
Sold_hour_L_3_month_REP = ((total_CP_revenue_L_3_month[total_CP_revenue_L_3_month['opcategory'] == 'REPAIR']['lbrsoldhours'].sum()) / 3) * 12
Rep_ELR_annual_ROI = 0
if float(Monthly_fees) != 0:
    Rep_ELR_annual_ROI = round_off(((round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP)) - (float(Monthly_fees)) * 12) / (float(Monthly_fees) * 12)) * 100)
# Initialize output dictionary
# Client_Report_Card = {
#     "Monthly FOPC": "$0",
#     "Monthly DMS": "$0",
#     "Total": "$0",
#     "ROI": "0%",
#     "Total Pts & Lbr GP Change": "$0",
#     "Repair ELR Change": "$0",
#     "KPIs": {},
#     "Competitive": {},
#     "Maintenance": {},
#     "Repair": {}
# }
Client_Report_Card ={"Monthly FOPC": float(fopc_fees),
                    "Monthly DMS": float(dms_fees),
                    "Total": Monthly_fees,
                    "ROI": Lbr_Pts_GP_1_month_ROI,
                    "Total Pts & Lbr GP Change": Labor_Parts_GP_1_month_variance,
                    "Repair ELR Change":round_off((float(Repair_ELR_1_month_variance)) * round_off(Sold_hour_L_month_REP)),
                    # [f"Monthly DMS ${float(dms_fees)}", f"ROI = {Lbr_Pts_GP_1_month_ROI}%", f"Based on Total Pts & Lbr GP Change of ${Labor_Parts_GP_1_month_variance}","" ,f"ROI = {Lbr_Pts_GP_annual_ROI}%",f"Based on Total Pts & Lbr GP Change of ${Labor_Parts_GP_annual_variance}",""],
                    # [f"Monthly FOPC ${float(fopc_fees)}"],
                    # [f"Total Monthly ${Monthly_fees}", f"ROI = {Rep_ELR_1_month_ROI}%",f"Based on Repair ELR Change of ${round_off((float(Repair_ELR_1_month_variance)) * round_off(Sold_hour_L_month_REP))}","",f"ROI = {Rep_ELR_annual_ROI}%",f"Based on Repair ELR Change of ${round_off(float(Repair_ELR_annual_variance) * round_off(Sold_hour_L_3_month_REP))}",""],
                    # ["Total Shop", "3 Month Avg / Baseline", "Last Month", "Variance", "Prior Annual Pace", "Current Annual Pace", "Variance"],
                    "KPIs": {
                    "RO Count": [RO_count_3_month_avg, RO_count_L_month, RO_count_1_month_variance, RO_count_prior_annual_pace, RO_count_current_annual_pace, RO_count_annual_variance],
                    "Hours Sold": [Sold_hours_3_month_avg, Sold_hours_L_month, Sold_hours_1_month_variance, Sold_hours_prior_annual_pace, Sold_hours_current_annual_pace, Sold_hours_annual_variance],
                    "Cust. Pay Hrs Per RO": [Hours_per_RO_3_month_avg, Hours_per_RO_L_month, Hours_per_RO_1_month_variance, Hours_per_RO_prior_annual_pace, Hours_per_RO_current_annual_pace, Hours_per_RO_annual_variance],
                    "Total Shop ELR": [Total_ELR_3_month, Total_ELR_L_month, Total_ELR_1_month_variance, Total_ELR_prior_annual_pace, Total_ELR_current_annual_pace, Total_ELR_annual_variance],
                    "Total Labor GP%":[Labor_GP_perc_3_month_avg, Labor_GP_perc_L_month, Labor_GP_perc_1_month_variance, Labor_GP_perc_prior_annual_pace, Labor_GP_perc_current_annual_pace, Labor_GP_perc_annual_variance],
                    "Total Parts GP%":[Parts_GP_perc_3_month_avg, Parts_GP_perc_L_month, Parts_GP_perc_1_month_variance, Parts_GP_perc_prior_annual_pace, Parts_GP_perc_current_annual_pace, Parts_GP_perc_annual_variance],
                    "Total Labor Sold":[Labor_sale_3_month_avg, Labor_sale_L_month, Labor_sale_1_month_variance, Labor_sale_prior_annual_pace, Labor_sale_current_annual_pace, Labor_sale_annual_variance],
                    "Total Labor GP":[Labor_GP_3_month_avg, Labor_GP_L_month, Labor_GP_1_month_variance, Labor_GP_prior_annual_pace, Labor_GP_current_annual_pace, Labor_GP_annual_variance],
                    "Total Parts Sale":[Parts_sale_3_month_avg, Parts_sale_L_month, Parts_sale_1_month_variance, Parts_sale_prior_annual_pace, Parts_sale_current_annual_pace, Parts_sale_annual_variance],
                    "Total Parts GP":[Parts_GP_3_month_avg, Parts_GP_L_month, Parts_GP_1_month_variance, Parts_GP_prior_annual_pace, Parts_GP_current_annual_pace, Parts_GP_annual_variance],
                    "Total Lbr & Pts Sales":[Labor_Parts_sale_3_month_avg, Labor_Parts_sale_L_month, Labor_Parts_sale_1_month_variance, Labor_Parts_sale_prior_annual_pace, Labor_Parts_sale_current_annual_pace, Labor_Parts_sale_annual_variance],
                    "Total Lbr & Pts GP":[Labor_Parts_GP_3_month_avg, Labor_Parts_GP_L_month, Labor_Parts_GP_1_month_variance, Labor_Parts_GP_prior_annual_pace, Labor_Parts_GP_current_annual_pace, Labor_Parts_GP_annual_variance]
},
"Competitive": {},
"Maintenance": {},
"Repair": {}
}
# print(Client_Report_Card,"Client_Report_Card")
# print(total_CP_revenue_3_month,"total_CP_revenue_3_month")
group_by_category_3_month = total_CP_revenue_3_month.groupby(['opcategory']).agg({
    'lbrsale' : 'sum',
    'lbrcost' : 'sum',
    'prtextendedsale' : 'sum',
    'prtextendedcost' : 'sum',
    'lbrsoldhours' : 'sum'
}).reset_index()
# print(group_by_category_3_month,"group_by_category_3_month...")
# group_by_category_3_month.to_csv('../Output/group_by_category_3_month.csv')

group_by_category_3_month = group_by_category_3_month.astype(float, errors='ignore')

if not group_by_category_3_month.empty:
    for index, rows in group_by_category_3_month.iterrows():
        group_by_category_3_month.at[index, "ELR"] = 0
        if rows['lbrsoldhours'] != 0:
            group_by_category_3_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']
            
        group_by_category_3_month.at[index, "Percentage of Sold Hours"] = 0
        if Sold_hours_3_month != 0:
            group_by_category_3_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_3_month) * 100
        
        group_by_category_3_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

        group_by_category_3_month.at[index, "Labor GP %"] = 0
        if rows['lbrsale'] != 0:
            group_by_category_3_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100
        
        group_by_category_3_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

        group_by_category_3_month.at[index, "Parts GP %"] = 0
        if rows['prtextendedsale'] != 0:
            group_by_category_3_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

        group_by_category_3_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

        group_by_category_3_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])
# print(total_CP_revenue_L_month,"total_CP_revenue_L_month............")
group_by_category_L_month = total_CP_revenue_L_month.groupby(['opcategory']).agg({
    'lbrsale' : 'sum',
    'lbrcost' : 'sum',
    'prtextendedsale' : 'sum',
    'prtextendedcost' : 'sum',
    'lbrsoldhours' : 'sum'
}).reset_index()

group_by_category_L_month = group_by_category_L_month.astype(float, errors='ignore')
# print(group_by_category_L_month,"group_by_category_L_month....")
if not group_by_category_L_month.empty:
    for index, rows in group_by_category_L_month.iterrows():
        group_by_category_L_month.at[index, "ELR"] = 0
        if rows['lbrsoldhours'] != 0:
            group_by_category_L_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']
            
        group_by_category_L_month.at[index, "Percentage of Sold Hours"] = 0
        if Sold_hours_L_month != 0:
            group_by_category_L_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_L_month) * 100

        group_by_category_L_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

        group_by_category_L_month.at[index, "Labor GP %"] = 0
        if rows['lbrsale'] != 0:
            group_by_category_L_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100
        
        group_by_category_L_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

        group_by_category_L_month.at[index, "Parts GP %"] = 0
        if rows['prtextendedsale'] != 0:
            group_by_category_L_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

        group_by_category_L_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

        group_by_category_L_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])

group_by_category_L_3_month = total_CP_revenue_L_3_month.groupby(['opcategory']).agg({
    'lbrsale' : 'sum',
    'lbrcost' : 'sum',
    'prtextendedsale' : 'sum',
    'prtextendedcost' : 'sum',
    'lbrsoldhours' : 'sum'
}).reset_index()

group_by_category_L_3_month = group_by_category_L_3_month.astype(float, errors='ignore')
# print(group_by_category_L_3_month,"group_by_category_L_3_month...")
if not group_by_category_L_3_month.empty:
    for index, rows in group_by_category_L_3_month.iterrows():
        group_by_category_L_3_month.at[index, "ELR"] = 0
        if rows['lbrsoldhours'] != 0:
            group_by_category_L_3_month.at[index, "ELR"] = rows['lbrsale'] / rows['lbrsoldhours']
            
        group_by_category_L_3_month.at[index, "Percentage of Sold Hours"] = 0
        if Sold_hours_L_3_month != 0:
            group_by_category_L_3_month.at[index, "Percentage of Sold Hours"] = (rows['lbrsoldhours'] / Sold_hours_L_3_month) * 100
        
        group_by_category_L_3_month.at[index, "Labor GP"] = rows['lbrsale'] - rows['lbrcost']

        group_by_category_L_3_month.at[index, "Labor GP %"] = 0
        if rows['lbrsale'] != 0:
            group_by_category_L_3_month.at[index, "Labor GP %"] = ((rows['lbrsale'] - rows['lbrcost']) / rows['lbrsale']) * 100
        
        group_by_category_L_3_month.at[index, "Parts GP"] = rows['prtextendedsale'] - rows['prtextendedcost']

        group_by_category_L_3_month.at[index, "Parts GP %"] = 0
        if rows['prtextendedsale'] != 0:
            group_by_category_L_3_month.at[index, "Parts GP %"] = ((rows['prtextendedsale'] - rows['prtextendedcost']) / rows['prtextendedsale']) * 100

        group_by_category_L_3_month.at[index, "Total Lbr & Pts Sale"] = rows['lbrsale'] + rows['prtextendedsale']

        group_by_category_L_3_month.at[index, "Total Lbr & Pts GP"] = (rows['lbrsale'] + rows['prtextendedsale']) - (rows['lbrcost'] + rows['prtextendedcost'])

with open("Json-files/client_report_3month_ui.json", "r") as file2:
    ui_data = json.load(file2)
# Extract available sections from UI data
available_sections = set()
if isinstance(ui_data, dict):
    available_sections = set(ui_data.keys())

# print(available_sections,"available_sections")
# Extract available sections
available_sections = set()
if isinstance(ui_data, dict):
    available_sections = set(ui_data.keys())
special_cases = {}
opcategory_list = ['COMPETITIVE','MAINTENANCE','REPAIR']
for cat in opcategory_list:
    # 3 month average values
    hours_sold_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3)
    hours_sold_3_month_avg = round_off(hours_sold_3_month_avg_value)
    ELR_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['ELR'].iloc[0])
    ELR_3_month = round_off(ELR_3_month_value, 2)
    sld_hours_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
    sld_hours_perc_3_month = round_off(sld_hours_perc_3_month_value)
    labor_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3)
    labor_sale_3_month_avg = round_off(labor_sale_3_month_avg_value)
    labor_GP_perc_3_month_value = group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
    labor_GP_perc_3_month = round_off(labor_GP_perc_3_month_value)
    labor_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3)
    labor_GP_3_month_avg = round_off(labor_GP_3_month_avg_value)
    parts_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3)
    parts_sale_3_month_avg = round_off(parts_sale_3_month_avg_value)
    parts_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3)
    parts_GP_3_month_avg = round_off(parts_GP_3_month_avg_value)
    parts_GP_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
    parts_GP_perc_3_month = round_off(parts_GP_perc_3_month_value)
    total_lbr_pts_sale_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3)
    total_lbr_pts_sale_3_month_avg = round_off(total_lbr_pts_sale_3_month_avg_value)
    total_lbr_pts_GP_3_month_avg_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3)
    total_lbr_pts_GP_3_month_avg = round_off(total_lbr_pts_GP_3_month_avg_value)


    # L month average values
    hours_sold_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['lbrsoldhours'].iloc[0])
    hours_sold_L_month = round_off(hours_sold_L_month_value)
    ELR_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['ELR'].iloc[0])
    ELR_L_month = round_off(ELR_L_month_value, 2)
    sld_hours_perc_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
    sld_hours_perc_L_month = round_off(sld_hours_perc_L_month_value)
    labor_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['lbrsale'].iloc[0])
    labor_sale_L_month = round_off(labor_sale_L_month_value)
    labor_GP_perc_L_month_value = group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Labor GP %'].iloc[0]
    labor_GP_perc_L_month = round_off(labor_GP_perc_L_month_value)
    labor_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Labor GP'].iloc[0])
    labor_GP_L_month = round_off(labor_GP_L_month_value)
    parts_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['prtextendedsale'].iloc[0])
    parts_sale_L_month = round_off(parts_sale_L_month_value)
    parts_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Parts GP'].iloc[0])
    parts_GP_L_month = round_off(parts_GP_L_month_value)
    parts_GP_perc_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Parts GP %'].iloc[0])
    parts_GP_perc_L_month = round_off(parts_GP_perc_L_month_value)
    total_lbr_pts_sale_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0])
    total_lbr_pts_sale_L_month = round_off(total_lbr_pts_sale_L_month_value)
    total_lbr_pts_GP_L_month_value = float(group_by_category_L_month[group_by_category_L_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0])
    total_lbr_pts_GP_L_month = round_off(total_lbr_pts_GP_L_month_value)

    # Baseline annual values
    hours_sold_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3) * 12)
    hours_sold_annual_3_month = round_off(hours_sold_annual_3_month_value)
    ELR_annual_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['ELR'].iloc[0])
    ELR_annual_3_month = round_off(ELR_3_month_value, 2)
    sld_hours_annual_perc_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
    sld_hours_annual_perc_3_month = round_off(sld_hours_perc_3_month_value)
    labor_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3) * 12)
    labor_sale_annual_3_month = round_off(labor_sale_annual_3_month_value)
    labor_GP_perc_annual_3_month_value = group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
    labor_GP_perc_annual_3_month = round_off(labor_GP_perc_annual_3_month_value)
    labor_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3) *12)
    labor_GP_annual_3_month = round_off(labor_GP_annual_3_month_value)
    parts_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3) *12)
    parts_sale_annual_3_month = round_off(parts_sale_annual_3_month_value)
    parts_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3) * 12)
    parts_GP_annual_3_month = round_off(parts_GP_annual_3_month_value)
    parts_GP_perc_annual_3_month_value = float(group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
    parts_GP_perc_annual_3_month = round_off(parts_GP_perc_3_month_value)
    total_lbr_pts_sale_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3) * 12)
    total_lbr_pts_sale_annual_3_month = round_off(total_lbr_pts_sale_annual_3_month_value)
    total_lbr_pts_GP_annual_3_month_value = float((group_by_category_3_month[group_by_category_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3) * 12)
    total_lbr_pts_GP_annual_3_month = round_off(total_lbr_pts_GP_annual_3_month_value)

    # L 3 month annual values
    hours_sold_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['lbrsoldhours'].iloc[0] / 3) * 12)
    hours_sold_L_3_month = round_off(hours_sold_L_3_month_value)
    ELR_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['ELR'].iloc[0])
    ELR_L_3_month = round_off(ELR_L_3_month_value, 2)
    sld_hours_perc_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Percentage of Sold Hours'].iloc[0])
    sld_hours_perc_L_3_month = round_off(sld_hours_perc_L_3_month_value)
    labor_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['lbrsale'].iloc[0] / 3) * 12)
    labor_sale_L_3_month = round_off(labor_sale_L_3_month_value)
    labor_GP_perc_L_3_month_value = group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Labor GP %'].iloc[0]
    labor_GP_perc_L_3_month = round_off(labor_GP_perc_L_3_month_value)
    labor_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Labor GP'].iloc[0] / 3) *12)
    labor_GP_L_3_month = round_off(labor_GP_L_3_month_value)
    parts_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['prtextendedsale'].iloc[0] / 3) *12)
    parts_sale_L_3_month = round_off(parts_sale_L_3_month_value)
    parts_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Parts GP'].iloc[0] / 3) * 12)
    parts_GP_L_3_month = round_off(parts_GP_L_3_month_value)
    parts_GP_perc_L_3_month_value = float(group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Parts GP %'].iloc[0])
    parts_GP_perc_L_3_month = round_off(parts_GP_perc_L_3_month_value)
    total_lbr_pts_sale_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Total Lbr & Pts Sale'].iloc[0] / 3) * 12)
    total_lbr_pts_sale_L_3_month = round_off(total_lbr_pts_sale_L_3_month_value)
    total_lbr_pts_GP_L_3_month_value = float((group_by_category_L_3_month[group_by_category_L_3_month['opcategory'] == cat]['Total Lbr & Pts GP'].iloc[0] / 3) * 12)
    total_lbr_pts_GP_L_3_month = round_off(total_lbr_pts_GP_L_3_month_value)
    # Client_Report_Card.append([f"{cat}"])
    # Client_Report_Card.append(["Hours Sold", hours_sold_3_month_avg, hours_sold_L_month, round_off(hours_sold_L_month - hours_sold_3_month_avg), hours_sold_annual_3_month, hours_sold_L_3_month, round_off(hours_sold_L_3_month - hours_sold_annual_3_month)])
    # Client_Report_Card.append(["ELR", ELR_3_month, ELR_L_month, round_off((ELR_L_month - ELR_3_month), 2), ELR_annual_3_month, ELR_L_3_month, round_off((ELR_L_3_month - ELR_annual_3_month), 2)])
    # Client_Report_Card.append(["GP %", labor_GP_perc_3_month, labor_GP_perc_L_month, round_off(labor_GP_perc_L_month - labor_GP_perc_3_month), labor_GP_perc_annual_3_month, labor_GP_perc_L_3_month, round_off(labor_GP_perc_L_3_month - labor_GP_perc_annual_3_month)])
    # Client_Report_Card.append(["% of Total Shop Hours", sld_hours_perc_3_month, sld_hours_perc_L_month, round_off(sld_hours_perc_L_month - sld_hours_perc_3_month), sld_hours_annual_perc_3_month, sld_hours_perc_L_3_month, round_off(sld_hours_perc_L_3_month - sld_hours_annual_perc_3_month)])
    # Client_Report_Card.append(["Labor Sold", labor_sale_3_month_avg, labor_sale_L_month, round_off(labor_sale_L_month - labor_sale_3_month_avg), labor_sale_annual_3_month, labor_sale_L_3_month, round_off(labor_sale_L_3_month - labor_sale_annual_3_month)])
    # Client_Report_Card.append(["Labor GP", labor_GP_3_month_avg, labor_GP_L_month, round_off(labor_GP_L_month - labor_GP_3_month_avg), labor_GP_annual_3_month, labor_GP_L_3_month, round_off(labor_GP_L_3_month - labor_GP_annual_3_month)])
    # Client_Report_Card.append(["Parts Sale", parts_sale_3_month_avg, parts_sale_L_month, round_off(parts_sale_L_month - parts_sale_3_month_avg), parts_sale_annual_3_month, parts_sale_L_3_month, round_off(parts_sale_L_3_month - parts_sale_annual_3_month)])
    # Client_Report_Card.append(["Parts GP", parts_GP_3_month_avg, parts_GP_L_month, round_off(parts_GP_L_month - parts_GP_3_month_avg), parts_GP_annual_3_month, parts_GP_L_3_month, round_off(parts_GP_L_3_month - parts_GP_annual_3_month)])
    # Client_Report_Card.append(["Parts GP %", parts_GP_perc_3_month, parts_GP_perc_L_month, round_off(parts_GP_perc_L_month - parts_GP_perc_3_month), parts_GP_perc_annual_3_month, parts_GP_perc_L_3_month, round_off(parts_GP_perc_L_3_month - parts_GP_perc_annual_3_month)])
    # Client_Report_Card.append(["Total Lbr & Pts Sale", total_lbr_pts_sale_3_month_avg, total_lbr_pts_sale_L_month, round_off(total_lbr_pts_sale_L_month - total_lbr_pts_sale_3_month_avg), total_lbr_pts_sale_annual_3_month, total_lbr_pts_sale_L_3_month, round_off(total_lbr_pts_sale_L_3_month - total_lbr_pts_sale_annual_3_month)])
    # Client_Report_Card.append(["Total Lbr & Pts GP", total_lbr_pts_GP_3_month_avg, total_lbr_pts_GP_L_month, round_off(total_lbr_pts_GP_L_month - total_lbr_pts_GP_3_month_avg), total_lbr_pts_GP_annual_3_month, total_lbr_pts_GP_L_3_month, round_off(total_lbr_pts_GP_L_3_month - total_lbr_pts_GP_annual_3_month)])
    # print([f"{cat}"])
    # print("cat.")
    # Client_Report_Card.append([f"{cat}"])
    # 

# List of operation categories
# opcategory_list = ['COMPETITIVE', 'MAINTENANCE', 'REPAIR']

# for opcategory in opcategory_list:
#     # print(f"Processing {opcategory}...")

#     # Ensure opcategory exists in both DataFrames
#     if opcategory in group_by_category_3_month['opcategory'].str.upper().unique() and \
#        opcategory in group_by_category_L_month['opcategory'].str.upper().unique():
        
#         # Filter data for current category
#         filtered_3_month = group_by_category_3_month[group_by_category_3_month['opcategory'].str.upper() == opcategory]
#         filtered_L_month = group_by_category_L_month[group_by_category_L_month['opcategory'].str.upper() == opcategory]

#         # Helper function to calculate variance
#         def calc_variance(value1, value2, decimals=0):
#             return round_off(value1 - value2, decimals)

#         # Helper function for annualized calculations
#         def annualize(value, factor=12):
#             return round_off(value * factor)

        # Create dictionary for special cases
    special_cases = {
        # Client_Report_Card.append(["Hours Sold": hours_sold_3_month_avg, hours_sold_L_month,
        #  round_off(hours_sold_L_month - hours_sold_3_month_avg), hours_sold_annual_3_month, hours_sold_L_3_month, 
        #  round_off(hours_sold_L_3_month - hours_sold_annual_3_month)])
        "Hours Sold": {
            "3 MTH Avg (Baseline)":hours_sold_3_month_avg ,
            "Last Month": hours_sold_L_month,
            "Variance":round_off(hours_sold_L_month - hours_sold_3_month_avg) ,
            "Prior Annual Pace":hours_sold_annual_3_month ,
            "Annual Pace":hours_sold_L_3_month ,
            "Variance Annualized":round_off(hours_sold_L_3_month - hours_sold_annual_3_month)
            
        },
        #  Client_Report_Card.append(["ELR": ELR_3_month, ELR_L_month, 
        # round_off((ELR_L_month - ELR_3_month), 2), ELR_annual_3_month, ELR_L_3_month, 
        # round_off((ELR_L_3_month - ELR_annual_3_month), 2)])
        "ELR": {
                "3 MTH Avg (Baseline)": ELR_3_month,
            "Last Month":ELR_L_month,
            "Variance": round_off((ELR_L_month - ELR_3_month), 2),
            "Prior Annual Pace": ELR_annual_3_month,
            "Annual Pace":ELR_L_3_month ,
            "Variance Annualized":round_off((ELR_L_3_month - ELR_annual_3_month), 2)
            # "3 MTH Avg (Baseline)": f"${round_off(float(filtered_3_month['ELR'].iloc[0]), 2)}",
            # "Last Month": f"${round_off(float(filtered_L_month['ELR'].iloc[0]), 2)}",
            # "Variance": f"${calc_variance(float(filtered_L_month['ELR'].iloc[0]), float(filtered_3_month['ELR'].iloc[0]), 2)}",
            # "Prior Annual Pace": f"${annualize(float(filtered_L_month['ELR'].iloc[0]))}",
            # "Annual Pace": f"${annualize(float(filtered_3_month['ELR'].iloc[0]), 4)}",
            # "Variance Annualized": f"${calc_variance(annualize(float(filtered_L_month['ELR'].iloc[0])), annualize(float(filtered_3_month['ELR'].iloc[0]), 4))}"
        },
        # Client_Report_Card.append(["GP %": labor_GP_perc_3_month, labor_GP_perc_L_month,
        #  round_off(labor_GP_perc_L_month - labor_GP_perc_3_month), labor_GP_perc_annual_3_month,
        #   labor_GP_perc_L_3_month, round_off(labor_GP_perc_L_3_month - labor_GP_perc_annual_3_month)])
        "Total Labor GP%": {
            "3 MTH Avg (Baseline)": labor_GP_perc_3_month,
            "Last Month":labor_GP_perc_L_month ,
            "Variance": round_off(labor_GP_perc_L_month - labor_GP_perc_3_month),
            "Prior Annual Pace": labor_GP_perc_annual_3_month,
            "Annual Pace": labor_GP_perc_L_3_month,
            "Variance Annualized":round_off(labor_GP_perc_L_3_month - labor_GP_perc_annual_3_month)
            # "3 MTH Avg (Baseline)": f"{round_off(float(filtered_3_month['Labor GP %'].iloc[0]))}%",
            # "Last Month": f"{round_off(float(filtered_L_month['Labor GP %'].iloc[0]))}%",
            # "Variance": f"{calc_variance(float(filtered_L_month['Labor GP %'].iloc[0]), float(filtered_3_month['Labor GP %'].iloc[0]))}%",
            # "Prior Annual Pace": f"{annualize(float(filtered_L_month['Labor GP %'].iloc[0]))}%",
            # "Annual Pace": f"{annualize(float(filtered_3_month['Labor GP %'].iloc[0]), 4)}%",
            # "Variance Annualized": f"{calc_variance(annualize(float(filtered_L_month['Labor GP %'].iloc[0])), annualize(float(filtered_3_month['Labor GP %'].iloc[0]), 4))}%"
        },
        #  Client_Report_Card.append(["Labor Sold": labor_sale_3_month_avg, labor_sale_L_month, 
        # round_off(labor_sale_L_month - labor_sale_3_month_avg), labor_sale_annual_3_month,
        #  labor_sale_L_3_month, round_off(labor_sale_L_3_month - labor_sale_annual_3_month)])
        "Total Labor Sold": {
            "3 MTH Avg (Baseline)": labor_sale_3_month_avg,
            "Last Month":labor_sale_L_month,
            "Variance":round_off(labor_sale_L_month - labor_sale_3_month_avg),
            "Prior Annual Pace": labor_sale_annual_3_month,
            "Annual Pace": labor_sale_L_3_month,
            "Variance Annualized": round_off(labor_sale_L_3_month - labor_sale_annual_3_month)
            # "3 MTH Avg (Baseline)": f"${round_off(float(filtered_3_month['lbrsale'].iloc[0] / 3))}",
            # "Last Month": f"${round_off(float(filtered_L_month['lbrsale'].iloc[0]))}",
            # "Variance": f"${calc_variance(float(filtered_L_month['lbrsale'].iloc[0]), float(filtered_3_month['lbrsale'].iloc[0] / 3))}",
            # "Prior Annual Pace": f"${annualize(float(filtered_L_month['lbrsale'].iloc[0]))}",
            # "Annual Pace": f"${annualize(float(filtered_3_month['lbrsale'].iloc[0] / 3), 4)}",
            # "Variance Annualized": f"${calc_variance(annualize(float(filtered_L_month['lbrsale'].iloc[0])), annualize(float(filtered_3_month['lbrsale'].iloc[0] / 3), 4))}"
        },
        # Client_Report_Card.append(["% of Total Shop Hours": sld_hours_perc_3_month, sld_hours_perc_L_month, round_off(sld_hours_perc_L_month - sld_hours_perc_3_month), sld_hours_annual_perc_3_month, sld_hours_perc_L_3_month, round_off(sld_hours_perc_L_3_month - sld_hours_annual_perc_3_month)])
        "% of Total Shop Hours":{
            "3 MTH Avg (Baseline)": sld_hours_perc_3_month,
            "Last Month": sld_hours_perc_L_month,
            "Variance":round_off(sld_hours_perc_L_month - sld_hours_perc_3_month),
            "Prior Annual Pace": sld_hours_annual_perc_3_month,
            "Annual Pace": sld_hours_perc_L_3_month,
            "Variance Annualized":  round_off(sld_hours_perc_L_3_month - sld_hours_annual_perc_3_month)   

            },
        # Client_Report_Card.append(["Labor GP": labor_GP_3_month_avg, labor_GP_L_month, round_off(labor_GP_L_month - labor_GP_3_month_avg),
        #  labor_GP_annual_3_month, labor_GP_L_3_month, round_off(labor_GP_L_3_month - labor_GP_annual_3_month)])
        "Total Labor GP":{
            "3 MTH Avg (Baseline)": labor_GP_3_month_avg,
            "Last Month": labor_GP_L_month,
            "Variance":round_off(labor_GP_L_month - labor_GP_3_month_avg),
            "Prior Annual Pace": labor_GP_annual_3_month,
            "Annual Pace": labor_GP_L_3_month,
            "Variance Annualized":  round_off(labor_GP_L_3_month - labor_GP_annual_3_month)
        

            },
        # Client_Report_Card.append(["Parts Sale": parts_sale_3_month_avg, parts_sale_L_month, 
        # round_off(parts_sale_L_month - parts_sale_3_month_avg), parts_sale_annual_3_month, parts_sale_L_3_month,
        #  round_off(parts_sale_L_3_month - parts_sale_annual_3_month)])
            "Total Parts Sale":{
            "3 MTH Avg (Baseline)": parts_sale_3_month_avg,
            "Last Month": parts_sale_L_month,
            "Variance": round_off(parts_sale_L_month - parts_sale_3_month_avg),
            "Prior Annual Pace": parts_sale_annual_3_month,
            "Annual Pace": parts_sale_L_3_month,
            "Variance Annualized":   round_off(parts_sale_L_3_month - parts_sale_annual_3_month)
        

            },
        #  Client_Report_Card.append(["Parts GP": parts_GP_3_month_avg, parts_GP_L_month, 
        # round_off(parts_GP_L_month - parts_GP_3_month_avg), parts_GP_annual_3_month, parts_GP_L_3_month,
        #  round_off(parts_GP_L_3_month - parts_GP_annual_3_month)])
        "Total Parts GP":{
            "3 MTH Avg (Baseline)": parts_GP_3_month_avg,
            "Last Month": parts_GP_L_month,
            "Variance": round_off(parts_GP_L_month - parts_GP_3_month_avg),
            "Prior Annual Pace": parts_GP_annual_3_month,
            "Annual Pace": parts_GP_L_3_month,
            "Variance Annualized":   round_off(parts_GP_L_3_month - parts_GP_annual_3_month)
        

            },
        # Client_Report_Card.append(["Parts GP %": parts_GP_perc_3_month, parts_GP_perc_L_month, 
        # round_off(parts_GP_perc_L_month - parts_GP_perc_3_month), parts_GP_perc_annual_3_month,
        #  parts_GP_perc_L_3_month, round_off(parts_GP_perc_L_3_month - parts_GP_perc_annual_3_month)])
        "Total Parts GP%":{
            "3 MTH Avg (Baseline)": parts_GP_perc_3_month,
            "Last Month": parts_GP_perc_L_month,
            "Variance": round_off(parts_GP_perc_L_month - parts_GP_perc_3_month),
            "Prior Annual Pace": parts_GP_perc_annual_3_month,
            "Annual Pace": parts_GP_perc_L_3_month,
            "Variance Annualized":  round_off(parts_GP_perc_L_3_month - parts_GP_perc_annual_3_month)
                    },
        # Client_Report_Card.append(["Total Lbr & Pts GP": total_lbr_pts_GP_3_month_avg, total_lbr_pts_GP_L_month,
        #  round_off(total_lbr_pts_GP_L_month - total_lbr_pts_GP_3_month_avg), total_lbr_pts_GP_annual_3_month,
        #   total_lbr_pts_GP_L_3_month, round_off(total_lbr_pts_GP_L_3_month - total_lbr_pts_GP_annual_3_month)])
        "Lbr & Pts GP": {
            "3 MTH Avg (Baseline)":total_lbr_pts_GP_3_month_avg,
            "Last Month":total_lbr_pts_GP_L_month,
            "Variance": round_off(total_lbr_pts_GP_L_month - total_lbr_pts_GP_3_month_avg),
            "Prior Annual Pace":total_lbr_pts_GP_annual_3_month,
            "Annual Pace": total_lbr_pts_GP_L_3_month,
            "Variance Annualized": round_off(total_lbr_pts_GP_L_3_month - total_lbr_pts_GP_annual_3_month)
            # "3 MTH Avg (Baseline)": f"${round_off(float(filtered_3_month['Total Lbr & Pts GP'].iloc[0] / 3))}",
            # "Last Month": f"${round_off(float(filtered_L_month['Total Lbr & Pts GP'].iloc[0]))}",
            # "Variance": f"${calc_variance(float(filtered_L_month['Total Lbr & Pts GP'].iloc[0]), float(filtered_3_month['Total Lbr & Pts GP'].iloc[0] / 3))}",
            # "Prior Annual Pace": f"${annualize(float(filtered_L_month['Total Lbr & Pts GP'].iloc[0]))}",
            # "Annual Pace": f"${annualize(float(filtered_3_month['Total Lbr & Pts GP'].iloc[0] / 3), 4)}",
            # "Variance Annualized": f"${calc_variance(annualize(float(filtered_L_month['Total Lbr & Pts GP'].iloc[0])), annualize(float(filtered_3_month['Total Lbr & Pts GP'].iloc[0] / 3), 4))}"
            },
        # Client_Report_Card.append(["Total Lbr & Pts Sale": total_lbr_pts_sale_3_month_avg, total_lbr_pts_sale_L_month, 
        # round_off(total_lbr_pts_sale_L_month - total_lbr_pts_sale_3_month_avg), total_lbr_pts_sale_annual_3_month, 
        # total_lbr_pts_sale_L_3_month, round_off(total_lbr_pts_sale_L_3_month - total_lbr_pts_sale_annual_3_month)])
        "Total Lbr & Pts Sales": {
                "3 MTH Avg (Baseline)": total_lbr_pts_sale_3_month_avg,
            "Last Month": total_lbr_pts_sale_L_month,
            "Variance":  round_off(total_lbr_pts_sale_L_month - total_lbr_pts_sale_3_month_avg),
            "Prior Annual Pace": total_lbr_pts_sale_annual_3_month,
            "Annual Pace": total_lbr_pts_sale_L_3_month,
            "Variance Annualized":   round_off(total_lbr_pts_sale_L_3_month - total_lbr_pts_sale_annual_3_month)
        }
    }

    #  Update Client_Report_Card
    Client_Report_Card[cat] = special_cases

       
print(Client_Report_Card,"Client_Report_Card....")
    # Client_Report_Card.append(["Hours Sold": hours_sold_3_month_avg, hours_sold_L_month, round_off(hours_sold_L_month - hours_sold_3_month_avg), hours_sold_annual_3_month, hours_sold_L_3_month, round_off(hours_sold_L_3_month - hours_sold_annual_3_month)])
    # Client_Report_Card.append(["ELR": ELR_3_month, ELR_L_month, round_off((ELR_L_month - ELR_3_month), 2), ELR_annual_3_month, ELR_L_3_month, round_off((ELR_L_3_month - ELR_annual_3_month), 2)])
    # Client_Report_Card.append(["GP %": labor_GP_perc_3_month, labor_GP_perc_L_month, round_off(labor_GP_perc_L_month - labor_GP_perc_3_month), labor_GP_perc_annual_3_month, labor_GP_perc_L_3_month, round_off(labor_GP_perc_L_3_month - labor_GP_perc_annual_3_month)])
    # Client_Report_Card.append(["% of Total Shop Hours": sld_hours_perc_3_month, sld_hours_perc_L_month, round_off(sld_hours_perc_L_month - sld_hours_perc_3_month), sld_hours_annual_perc_3_month, sld_hours_perc_L_3_month, round_off(sld_hours_perc_L_3_month - sld_hours_annual_perc_3_month)])
    # Client_Report_Card.append(["Labor Sold": labor_sale_3_month_avg, labor_sale_L_month, round_off(labor_sale_L_month - labor_sale_3_month_avg), labor_sale_annual_3_month, labor_sale_L_3_month, round_off(labor_sale_L_3_month - labor_sale_annual_3_month)])
    # Client_Report_Card.append(["Labor GP": labor_GP_3_month_avg, labor_GP_L_month, round_off(labor_GP_L_month - labor_GP_3_month_avg), labor_GP_annual_3_month, labor_GP_L_3_month, round_off(labor_GP_L_3_month - labor_GP_annual_3_month)])
    # Client_Report_Card.append(["Parts Sale": parts_sale_3_month_avg, parts_sale_L_month, round_off(parts_sale_L_month - parts_sale_3_month_avg), parts_sale_annual_3_month, parts_sale_L_3_month, round_off(parts_sale_L_3_month - parts_sale_annual_3_month)])
    # Client_Report_Card.append(["Parts GP": parts_GP_3_month_avg, parts_GP_L_month, round_off(parts_GP_L_month - parts_GP_3_month_avg), parts_GP_annual_3_month, parts_GP_L_3_month, round_off(parts_GP_L_3_month - parts_GP_annual_3_month)])
    # Client_Report_Card.append(["Parts GP %": parts_GP_perc_3_month, parts_GP_perc_L_month, round_off(parts_GP_perc_L_month - parts_GP_perc_3_month), parts_GP_perc_annual_3_month, parts_GP_perc_L_3_month, round_off(parts_GP_perc_L_3_month - parts_GP_perc_annual_3_month)])
    # Client_Report_Card.append(["Total Lbr & Pts Sale": total_lbr_pts_sale_3_month_avg, total_lbr_pts_sale_L_month, round_off(total_lbr_pts_sale_L_month - total_lbr_pts_sale_3_month_avg), total_lbr_pts_sale_annual_3_month, total_lbr_pts_sale_L_3_month, round_off(total_lbr_pts_sale_L_3_month - total_lbr_pts_sale_annual_3_month)])
    # Client_Report_Card.append(["Total Lbr & Pts GP": total_lbr_pts_GP_3_month_avg, total_lbr_pts_GP_L_month, round_off(total_lbr_pts_GP_L_month - total_lbr_pts_GP_3_month_avg), total_lbr_pts_GP_annual_3_month, total_lbr_pts_GP_L_3_month, round_off(total_lbr_pts_GP_L_3_month - total_lbr_pts_GP_annual_3_month)])
# print(Client_Report_Card,"Client_Report_Card")
json_output_path = "Json-files/results_set_client_report_3_monthtest.json"
# with open(json_output_path, "w") as json_file:
#     json.dump(Client_Report_Card, json_file, indent=4)
# Client_Report_Card.to_csv('../Output/client_report3.csv')

# Convert DataFrame to dictionary (records format is usually preferred)
if isinstance(Client_Report_Card, pd.DataFrame):
    Client_Report_Card = Client_Report_Card.to_dict(orient="records")

# json_output_path = "Json-files/results_set_client_report.json"
with open(json_output_path, "w") as json_file:
    json.dump(Client_Report_Card, json_file, indent=4)

  #Write data to Excel
# output_df = pd.DataFrame(Client_Report_Card)

# # Define the path to the Excel file
# path = '../Output/results_set.xlsx'

# # Ensure the directory exists
# output_dir = os.path.dirname(path)
# os.makedirs(output_dir, exist_ok=True)

# # Try to load the existing workbook or create a new one if it doesn't exist
# try:
#     workbook = openpyxl.load_workbook(path)
# except FileNotFoundError:
#     workbook = openpyxl.Workbook()  # Create a new workbook
# # Save the workbook after deleting the sheet (if it was deleted)
# workbook.save(path)

# sheet_name = 'Client Report card 3 Month'
# if sheet_name in workbook.sheetnames:
#     del workbook[sheet_name]

# workbook.save(path)

# # Now write the DataFrame to the Excel file
# with pd.ExcelWriter(path, engine='openpyxl', mode='a') as writer:
#     output_df.to_excel(writer, sheet_name=sheet_name, index=False)

# # Optionally, check if there is a default sheet ("Sheet" or "Sheet1") and remove it
# default_sheet_name = "Sheet"  # or "Sheet1"
# if default_sheet_name in workbook.sheetnames:
#     del workbook[default_sheet_name]
# print("Client report card - 3 month data calculation is completed successfully!!")