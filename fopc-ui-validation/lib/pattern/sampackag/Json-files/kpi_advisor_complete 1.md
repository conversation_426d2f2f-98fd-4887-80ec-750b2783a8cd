[{"KPI Scorecard A - Financial - Customer Pay": null, "Labor Sales": "$408,970", "Labor Gross Profit": "$303,354 / 74.2% ", "Labor Sales Per RO": "$262", "Labor GP Per RO": "$194", "Parts Sales": "$386,334", "Parts Gross Profit": "$123,829 / 32.1%", "Parts Sales Per RO": "$247", "Parts GP Per RO": "$79", "Labor + Parts Sales": "$795,304", "Labor + Parts Gross Profit": "$427,183", "Parts to Labor Ratio": "$0.9 to $1", "B) Pricing - Customer Pay": null, "Repair Price Targets / Misses / Non-Compliance %": "506 / 46 / 9%", "Parts Price Targets / Misses / Non-Compliance %": "754 / 269 / 36%", "Competitive Hours / Sales / ELR": "738.3 / $35,158 / $48", "Maintenance Hours / Sales / ELR": "1,061.7 / $80,744 / $76", "Repair Hours / Sales / ELR": "1,698.7 / $293,068 / $173", "Total Hours / Total Sales / Total ELR": "3,498.7 / $408,970 / $117", "What-If Repair ELR if Non-Compliance at 0%": "$172 / $111", "Maintenance / Repair Work Mix": "51% / 49%", "C) Volume": null, "Customer Pay ROs": "1,562", "Warranty ROs": "1,056", "Internal ROs": "442", "All Unique ROs": "3060", "Average ROs Per Day": "129", "Representing What % of Total": "100%", "Average Days ROs are Open": "5.8", "Average Vehicle Age": "4.5 Years", "Average Miles Per Vehicle": "63,477 Miles", "All Sold Hours": "7,724.8", "Average Hours Sold Per Day": "325.3", "Customer Pay Hours Average Per RO": "2.2"}, {"D) Opportunities - CP Vehicles Under 60K Miles": null, "Total Count / % of Business": "777 / 50%", "1 Line Count / % Under 60K": "467 / 60%", "Labor Sold Per 1 Line RO": "$43", "Parts Sold Per 1 Line RO": "$69", "Total Sold Per 1 Line RO": "$112", "Labor Sold Per Multi-Line RO": "$223", "Parts Sold Per Multi-Line RO": "$226", "Total Sold Per Multi-Line RO": "$449", "Average Jobs Per Multi-Line RO": "2.8", "G) Opportunities - CP Vehicles Over 60K Miles": null, "1 Line Count / % Over 60K": "338 / 43%", "Labor Sold Per 1-Line RO-Above": "$61", "Parts Sold Per 1-Line RO-Above": "$67", "Total Sold Per 1-Line RO-Above": "$128", "Labor Sold Per Multi-Line RO-Above": "$669", "Parts Sold Per Multi-Line RO-Above": "$585", "Total Sold Per Multi-Line RO-Above": "$1254", "Average Jobs Per Multi-Line RO-Above": "3.5"}, {"E) Opportunities - MPI (CP and Wty)": null, "Opportunities Completed %": "2,520 / 1,940 / 77%", "Upsell Potential $": "$572,238", "Sold $ / % Collected": "$516,619 / 90%", "Potential Hours/Sold Hours/%": "2,520 / 2,127.2 / 84%", "Hours Sold Per Completed": "1.1"}, {"F) Opportunities - Menu Sales (CP and Wty)": null, "Opportunities Sold %": "0 / 0 / 0%", "Upsell Potential $-Menu": "$0", "Sold $ / % Collected-Menu": "$0 / 0%", "Potential Hours/Sold Hours/%-Menu": "0 / 0 / 0%", "Hours Sold Per Menu ": "0.0"}, {"Goal": {"1 Line Count / % Over 60K": "35%", "1 Line Count / % Under 60K": "20%", "<b>Parts Price Targets </b>/ Misses / <b>Non-Compliance %</b>": "10%", "All ROs - Average Days Open": "6%", "Average Vehicle Age": null, "Competitive Hours / Sales / ELR": null, "Labor + Parts Gross Profit": null, "Labor Gross Profit": "74%", "Labor Sales": null, "Labor Sales / GP $ / GP %": null, "Maintenance / Repair Work Mix": "38%", "Parts Price Targets / Misses / % of Non-Compliance": null, "Parts Sales / GP $ / GP %": null, "Pricing - Customer Pay": null, "Repair Price Targets / Misses / % of Non-Compliance": "5%", "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle": "2.2%", "Warranty Vehicles": null}}, {"Variance": {"1 Line Count / % Over 60K": "8%", "1 Line Count / % Under 60K": "40%", "<b>Parts Price Targets </b>/ Misses / <b>Non-Compliance %</b>": "26", "All ROs - Average Days Open": "0", "Average Vehicle Age": null, "Competitive Hours / Sales / ELR": null, "Labor + Parts Gross Profit": null, "Labor Gross Profit": "0.2%", "Labor Sales": null, "Labor Sales / GP $ / GP %": null, "Maintenance / Repair Work Mix": "13", "Parts Price Targets / Misses / % of Non-Compliance": null, "Parts Sales / GP $ / GP %": "-2.9%", "Pricing - Customer Pay": null, "Repair Price Targets / Misses / % of Non-Compliance": "4", "Total Sold Hrs / Avg Hrs Per Day / CP Avg Hrs Per Vehicle": "0.0", "Warranty Vehicles": null}}]