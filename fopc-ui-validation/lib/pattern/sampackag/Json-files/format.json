[
    {
        "A) Financial - Customer Pay": null,
        "Labor Sales": value,
        "Labor Gross Profit": "value/ %value",
        "Labor Sales Per RO":value,
        "Labor GP Per RO": value,
        "Parts Sales": value,
        "Parts Gross Profit": "value /%value",        
        "Parts Sales Per RO": value,
        "Parts GP Per RO": value,
        "Labor + Parts Sales": value,
        "Labor + Parts Gross Profit": value,
        "Parts to Labor Ratio": value,
        "B) Pricing - Customer Pay": null,
        "Repair Price Targets / Misses / Non-Compliance %": "value/value",
        "Parts Price Targets / Misses / Non-Compliance %":  "value/value",
        "Competitive Hours / Sales / ELR": "value/ value / value",
        "Maintenance Hours / Sales / ELR": "value / value / value",
        "Repair Hours / Sales / ELR": "value / value / value",
        "Total Hours / Total Sales / Total ELR": "value / value / value",
        "What-If % Repair ELR if Non-Compliance at 0%": value,
        "What-If % Total ELR if Repair Non-Compliance at 0%": value,
        "Maintenance / Repair Work Mix": "value/ value",
        "C) Volume": null,
        "Customer Pay ROs": value,
        "Warranty ROs": value,
        "Internal ROs": value,
        "All Unique ROs": value,
        "Average ROs Per Day": value,
        "Representing What % of Total": value,
        "Average Days ROs are Open": value,
        "Average Vehicle Age": value,
        "Average Miles Per Vehicle": value,
        "All Sold Hours": value,
        "Average Hours Sold Per Day": value,
        "Customer Pay Hours Average Per RO": value,   
        "D) Opportunities - CP Vehicles Under 60K Miles": null,
        "Total Count / % of Business": "value/value%",        
        "1 Line Count / % Under 60K": "value/value%",        
        "Labor Sold Per 1 Line RO": value,
        "Parts Sold Per 1 Line RO": value,
        "Total Sold Per 1 Line RO": value,
        "Labor Sold Per Multi-Line RO": value,
        "Parts Sold Per Multi-Line RO": value,
        "Total Sold Per Multi-Line RO": value,
        "Average Jobs Per Multi-Line RO": value,           
        "E) Opportunities - MPI (CP and Wty)": null,
        "Opportunities Completed %": "value/ value/ value%",        
        "Upsell Potential $": value,
        "Sold $ / % Collected": "value/value%",
        "Potential Hours/Sold Hours/%": "value/value/value%",
        "Hours Sold Per Completed": value,
        "F) Opportunities - Menu Sales (CP and Wty)": null,
        "Opportunities Sold %": "value/ value/ value%",        
        "Upsell Potential $": value,
        "Sold $ / % Collected": "value/value%",
        "Potential Hours/Sold Hours/%": "value/value/value%",        
        "Hours Sold Per Menu ": value,
        "G) Opportunities - CP Vehicles Over 60K Miles": null,
        "Total Count / % of Business": "value/value%",
        "1 Line Count / % Over 60K": "value/value%",
        "Labor Sold Per 1-Line RO": value,
        "Parts Sold Per 1-Line RO": value,
        "Total Sold Per 1-Line RO": value,
        "Labor Sold Per Multi-Line RO": value,
        "Parts Sold Per Multi-Line RO": value,
        "Total Sold Per Multi-Line RO": value,
        "Average Jobs Per Multi-Line RO": value
    }
]