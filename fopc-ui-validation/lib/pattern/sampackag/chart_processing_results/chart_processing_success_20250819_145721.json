[{"task_id": "Browser_1_942_point_0", "chart_id": "942", "chart_title": "CP Revenue", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 12, "value": "373977.76", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 332.72220744492137, "canvasX": 767.9088311754568, "canvasY": 88.72220744492135, "datasetLabel": "Labor Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Labor Revenue", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 0, "pointIndex": 12, "value": "373977.76", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 332.72220744492137, "canvasX": 767.9088311754568, "canvasY": 88.72220744492135, "datasetLabel": "Labor Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:19.875175", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:55:27.200362", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_1"}, {"task_id": "Browser_1_942_point_1", "chart_id": "942", "chart_title": "CP Revenue", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 12, "value": "378570.36", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 332.16838115377516, "canvasX": 767.9088311754568, "canvasY": 88.16838115377516, "datasetLabel": "Parts Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Parts Revenue", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 42.51619755043903}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 1, "pointIndex": 12, "value": "378570.36", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 332.16838115377516, "canvasX": 767.9088311754568, "canvasY": 88.16838115377516, "datasetLabel": "Parts Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:50.886895", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:55:58.184810", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "parallel_processing", "browser_id": "Browser_1"}, {"task_id": "Browser_1_942_point_2", "chart_id": "942", "chart_title": "CP Revenue", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 12, "value": "752548.12", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 287.0700238415852, "canvasX": 767.9088311754568, "canvasY": 43.070023841585225, "datasetLabel": "Combined Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Combined Revenue", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 43.070023841585225}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 0, "datasetIndex": 2, "pointIndex": 12, "value": "752548.12", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 287.0700238415852, "canvasX": 767.9088311754568, "canvasY": 43.070023841585225, "datasetLabel": "Combined Revenue", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:56:20.363287", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Combined Revenue", "value": "$752,548.12", "html_structure": {"h5_html": "Combined Revenue", "h6_html": " $752,548.12"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Combined Revenue", "value": "$752,548.12", "html_structure": {"h5_html": "Combined Revenue", "h6_html": " $752,548.12"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Combined Revenue", "value": "$752,548.12", "html_structure": {"h5_html": "Combined Revenue", "h6_html": " $752,548.12"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:56:27.635960", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "parallel_processing", "browser_id": "Browser_1"}, {"task_id": "Browser_2_939_point_0", "chart_id": "939", "chart_title": "CP Gross Profit", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 12, "value": "269110.96", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 321.0289285278973, "canvasX": 767.9088311754568, "canvasY": 77.02892852789729, "datasetLabel": "Labor Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Labor Gross Profit", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 0, "pointIndex": 12, "value": "269110.96", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 321.0289285278973, "canvasX": 767.9088311754568, "canvasY": 77.02892852789729, "datasetLabel": "Labor Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:19.422524", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:55:26.795946", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_2"}, {"task_id": "Browser_2_939_point_1", "chart_id": "939", "chart_title": "CP Gross Profit", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 12, "value": "122867.11", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 351.8913933266544, "canvasX": 767.9088311754568, "canvasY": 107.89139332665441, "datasetLabel": "Parts Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Parts Gross Profit", "click_result": {"success": true, "method": "coordinate_single"}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "coordinate_single"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 1, "pointIndex": 12, "value": "122867.11", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 351.8913933266544, "canvasX": 767.9088311754568, "canvasY": 107.89139332665441, "datasetLabel": "Parts Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:53.518736", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:56:01.237593", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "parallel_processing", "browser_id": "Browser_2"}, {"task_id": "Browser_2_939_point_2", "chart_id": "939", "chart_title": "CP Gross Profit", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 12, "value": "391978.07", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 295.09975709744043, "canvasX": 767.9088311754568, "canvasY": 51.099757097440445, "datasetLabel": "Combined Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Combined Gross Profit", "click_result": {"success": true, "method": "coordinate_single"}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "coordinate_single"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 1, "datasetIndex": 2, "pointIndex": 12, "value": "391978.07", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 295.09975709744043, "canvasX": 767.9088311754568, "canvasY": 51.099757097440445, "datasetLabel": "Combined Gross Profit", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:56:26.438159", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Labor Gross Profit", "value": "$269,110.96", "html_structure": {"h5_html": "Labor Gross Profit", "h6_html": " $269,110.96"}}, {"item_index": 6, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}, {"item_index": 7, "title": "Combined Gross Profit", "value": "$391,978.07", "html_structure": {"h5_html": "Combined Gross Profit", "h6_html": " $391,978.07"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Labor Gross Profit", "value": "$269,110.96", "html_structure": {"h5_html": "Labor Gross Profit", "h6_html": " $269,110.96"}}, {"item_index": 6, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}, {"item_index": 7, "title": "Combined Gross Profit", "value": "$391,978.07", "html_structure": {"h5_html": "Combined Gross Profit", "h6_html": " $391,978.07"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Labor Gross Profit", "value": "$269,110.96", "html_structure": {"h5_html": "Labor Gross Profit", "h6_html": " $269,110.96"}}, {"item_index": 6, "title": "Parts Gross Profit", "value": "$122,867.11", "html_structure": {"h5_html": "Parts Gross Profit", "h6_html": " $122,867.11"}}, {"item_index": 7, "title": "Combined Gross Profit", "value": "$391,978.07", "html_structure": {"h5_html": "Combined Gross Profit", "h6_html": " $391,978.07"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:56:34.096237", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "parallel_processing", "browser_id": "Browser_2"}, {"task_id": "Browser_3_940_point_0", "chart_id": "940", "chart_title": "CP Gross Profit Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 12, "value": "0.720", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 536.2648225902844, "canvasX": 767.9088311754568, "canvasY": 12.264822590284455, "datasetLabel": "Labor Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Labor Gross Profit %", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 0, "pointIndex": 12, "value": "0.720", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 536.2648225902844, "canvasX": 767.9088311754568, "canvasY": 12.264822590284455, "datasetLabel": "Labor Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:18.628545", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:55:26.625462", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_3"}, {"task_id": "Browser_3_940_point_1", "chart_id": "940", "chart_title": "CP Gross Profit Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 12, "value": "0.325", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 602.951653362363, "canvasX": 767.9088311754568, "canvasY": 78.95165336236305, "datasetLabel": "Parts Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Parts Gross Profit %", "click_result": {"success": true, "method": "coordinate_single"}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "coordinate_single"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 1, "pointIndex": 12, "value": "0.325", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 602.951653362363, "canvasX": 767.9088311754568, "canvasY": 78.95165336236305, "datasetLabel": "Parts Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:55:58.270932", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit %", "value": "32.5%", "html_structure": {"h5_html": "Parts Gross Profit %", "h6_html": " 32.5%"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit %", "value": "32.5%", "html_structure": {"h5_html": "Parts Gross Profit %", "h6_html": " 32.5%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 2, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 3, "title": "Parts Gross Profit %", "value": "32.5%", "html_structure": {"h5_html": "Parts Gross Profit %", "h6_html": " 32.5%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 2}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:56:02.522959", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 2, "method": "parallel_processing", "browser_id": "Browser_3"}, {"task_id": "Browser_3_940_point_2", "chart_id": "940", "chart_title": "CP Gross Profit Percentage", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 12, "value": "0.521", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 569.8614791058379, "canvasX": 767.9088311754568, "canvasY": 45.86147910583797, "datasetLabel": "Combined Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Combined Gross Profit %", "click_result": {"success": true, "method": "coordinate_single"}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "coordinate_single"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 2, "datasetIndex": 2, "pointIndex": 12, "value": "0.521", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 569.8614791058379, "canvasX": 767.9088311754568, "canvasY": 45.86147910583797, "datasetLabel": "Combined Gross Profit %", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:56:28.408309", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Labor Gross Profit %", "value": "72.0%", "html_structure": {"h5_html": "Labor Gross Profit %", "h6_html": " 72.0%"}}, {"item_index": 6, "title": "Parts Gross Profit %", "value": "32.5%", "html_structure": {"h5_html": "Parts Gross Profit %", "h6_html": " 32.5%"}}, {"item_index": 7, "title": "Combined Gross Profit %", "value": "52.1%", "html_structure": {"h5_html": "Combined Gross Profit %", "h6_html": " 52.1%"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Total Parts Sale", "value": "$378,570.36", "html_structure": {"h5_html": "Total Parts Sale", "h6_html": " $378,570.36"}}, {"item_index": 3, "title": "Total Parts Cost", "value": "$255,703.25", "html_structure": {"h5_html": "Total Parts Cost", "h6_html": " $255,703.25"}}, {"item_index": 4, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}, {"item_index": 5, "title": "Labor Gross Profit %", "value": "72.0%", "html_structure": {"h5_html": "Labor Gross Profit %", "h6_html": " 72.0%"}}, {"item_index": 6, "title": "Parts Gross Profit %", "value": "32.5%", "html_structure": {"h5_html": "Parts Gross Profit %", "h6_html": " 32.5%"}}, {"item_index": 7, "title": "Combined Gross Profit %", "value": "52.1%", "html_structure": {"h5_html": "Combined Gross Profit %", "h6_html": " 52.1%"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:56:35.958733", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 3, "method": "parallel_processing", "browser_id": "Browser_3"}, {"task_id": "Browser_1_920_point_0", "chart_id": "920", "chart_title": "Labor Sold Hours", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 12, "value": "3619.00", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 566.1725999859141, "canvasX": 767.9088311754568, "canvasY": 42.17259998591413, "datasetLabel": "Labor Sold Hours", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Labor Sold Hours", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 3, "datasetIndex": 0, "pointIndex": 12, "value": "3619.00", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 566.1725999859141, "canvasX": 767.9088311754568, "canvasY": 42.17259998591413, "datasetLabel": "Labor Sold Hours", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:57:12.544970", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:57:20.040379", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_1"}, {"task_id": "Browser_2_946_point_0", "chart_id": "946", "chart_title": "Effective Labor Rate", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 12, "value": "103.34", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 850.5874370104455, "canvasX": 767.9088311754568, "canvasY": 46.5874370104454, "datasetLabel": "Effective Labor Rate", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Effective Labor Rate", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 4, "datasetIndex": 0, "pointIndex": 12, "value": "103.34", "xLabel": "2023-11-01", "screenX": 1042.9088311754567, "screenY": 850.5874370104455, "canvasX": 767.9088311754568, "canvasY": 46.5874370104454, "datasetLabel": "Effective Labor Rate", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:57:12.672635", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:57:20.717547", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_2"}, {"task_id": "Browser_3_1238_point_0", "chart_id": "1238", "chart_title": "Parts Markup", "target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 12, "value": "1.4805", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 844.0896916956597, "canvasX": 767.9088311754568, "canvasY": 40.08969169565965, "datasetLabel": "Parts Markup", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "dataset_label": "Parts Markup", "click_result": {"success": true, "method": "chartjs_event", "position": {"x": 767.9088311754568, "y": 54.8984394607789}}, "navigation_result": {"success": true, "url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "method": "chartjs_event"}, "extracted_data": {"target_month_year": "2023-11-01", "point_data": {"canvasIndex": 5, "datasetIndex": 0, "pointIndex": 12, "value": "1.4805", "xLabel": "2023-11-01", "screenX": 1849.9088311754567, "screenY": 844.0896916956597, "canvasX": 767.9088311754568, "canvasY": 40.08969169565965, "datasetLabel": "Parts Markup", "chartType": "line", "coordinatesValid": true, "targetMonthYear": "2023-11-01"}, "click_success": true, "navigation_success": true, "extraction_data": {"extraction_timestamp": "2025-08-19T14:57:12.667595", "page_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "mui_grid_data": [{"container_index": 0, "selector_used": ".MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-3", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": ".MuiGrid-root.MuiGrid-container", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}, {"container_index": 1, "selector_used": "[class*=\"MuiGrid-container\"]", "items": [{"item_index": 0, "title": "Labor Sale - Customer Pay", "value": "$373,977.76", "html_structure": {"h5_html": "Labor Sale - Customer Pay", "h6_html": " $373,977.76"}}, {"item_index": 1, "title": "Total Labor Cost", "value": "$104,866.80", "html_structure": {"h5_html": "Total Labor Cost", "h6_html": " $104,866.80"}}, {"item_index": 2, "title": "Labor Sold Hours", "value": "3,619.00", "html_structure": {"h5_html": "Labor Sold Hours", "h6_html": " 3,619.00"}}]}], "all_text_content": [], "raw_html_sections": [], "monetary_data": [], "success": true, "error": null, "attempt": 1}, "error": null, "processing_time": 0, "screenshot_path": null}, "timestamp": "2025-08-19T14:57:20.772393", "success": true, "legend_controlled": true, "drilldown_url": "https://sampackag.fixedops.cc/AnalyzeData?chartId=drillDown", "click_success": true, "navigation_success": true, "extraction_success": true, "point_sequence": 1, "method": "parallel_processing", "browser_id": "Browser_3"}]