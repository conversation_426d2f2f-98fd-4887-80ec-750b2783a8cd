2025-08-18 15:45:45,952 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250818_154545.log
2025-08-18 15:45:45,955 - __main__ - INFO - main:4942 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-18 15:45:45,955 - __main__ - INFO - main:4946 - Start Time: 2025-08-18 15:45:45
2025-08-18 15:45:45,955 - __main__ - INFO - __init__:211 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-18 15:45:45,958 - __main__ - INFO - main:4971 - Valid authentication found, proceeding with processing...
2025-08-18 15:45:45,958 - __main__ - INFO - main:4974 - Starting parallel chart processing workflow with 4 browsers...
2025-08-18 15:45:46,478 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:45:46,781 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:46:03,067 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:46:11,774 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:46:12,077 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:46:26,894 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: 2023-11-01
2025-08-18 15:46:26,909 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: Nov 2023
2025-08-18 15:46:26,930 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: November 2023
2025-08-18 15:46:26,942 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: nov
2025-08-18 15:46:26,955 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: Nov
2025-08-18 15:46:26,967 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: 2023-11-01
2025-08-18 15:46:26,981 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: Nov 2023
2025-08-18 15:46:26,994 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: November 2023
2025-08-18 15:46:27,006 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: nov
2025-08-18 15:46:27,020 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: Nov
2025-08-18 15:46:27,034 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: 2023-11-01
2025-08-18 15:46:27,045 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: Nov 2023
2025-08-18 15:46:27,060 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: November 2023
2025-08-18 15:46:27,072 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: nov
2025-08-18 15:46:27,084 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: Nov
2025-08-18 15:46:27,096 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: 2023-11-01
2025-08-18 15:46:27,106 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: Nov 2023
2025-08-18 15:46:27,121 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: November 2023
2025-08-18 15:46:27,133 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: nov
2025-08-18 15:46:27,145 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: Nov
2025-08-18 15:46:27,157 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: 2023-11-01
2025-08-18 15:46:27,167 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: Nov 2023
2025-08-18 15:46:27,180 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: November 2023
2025-08-18 15:46:27,192 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: nov
2025-08-18 15:46:27,203 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: Nov
2025-08-18 15:46:27,215 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: 2023-11-01
2025-08-18 15:46:27,225 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: Nov 2023
2025-08-18 15:46:27,236 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: November 2023
2025-08-18 15:46:27,250 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: nov
2025-08-18 15:46:27,263 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: Nov
2025-08-18 15:46:27,276 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: 2023-11-01
2025-08-18 15:46:27,286 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: Nov 2023
2025-08-18 15:46:27,299 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: November 2023
2025-08-18 15:46:27,313 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: nov
2025-08-18 15:46:27,324 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: Nov
2025-08-18 15:46:27,336 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: 2023-11-01
2025-08-18 15:46:27,346 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: Nov 2023
2025-08-18 15:46:27,357 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: November 2023
2025-08-18 15:46:27,371 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: nov
2025-08-18 15:46:27,385 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: Nov
2025-08-18 15:46:27,398 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: 2023-11-01
2025-08-18 15:46:27,410 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: Nov 2023
2025-08-18 15:46:27,422 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: November 2023
2025-08-18 15:46:27,436 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: nov
2025-08-18 15:46:27,450 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: Nov
2025-08-18 15:46:27,467 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: 2023-11-01
2025-08-18 15:46:27,477 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: Nov 2023
2025-08-18 15:46:27,488 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: November 2023
2025-08-18 15:46:27,500 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: nov
2025-08-18 15:46:27,512 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: Nov
2025-08-18 15:46:27,525 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: 2023-11-01
2025-08-18 15:46:27,535 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: Nov 2023
2025-08-18 15:46:27,546 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: November 2023
2025-08-18 15:46:27,558 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: nov
2025-08-18 15:46:27,569 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: Nov
2025-08-18 15:46:28,501 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:46:28,516 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:46:28,528 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:46:28,554 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:46:28,915 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:46:29,071 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:46:29,098 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:46:29,213 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:47:00,867 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:47:01,945 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:47:02,728 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:47:05,131 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:47:37,179 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:47:37,208 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:47:37,220 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:47:37,261 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:47:37,746 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:47:37,755 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:47:37,768 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:47:37,892 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:48:05,991 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:48:06,008 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:48:07,819 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:48:38,676 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:48:38,680 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:48:38,714 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 15:48:39,078 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:48:39,211 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:48:39,221 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 15:49:07,132 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:49:07,319 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:49:09,461 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 15:49:38,157 - __main__ - INFO - main:4978 - Parallel processing with 4 browsers completed successfully!
2025-08-18 15:49:38,157 - __main__ - INFO - main:4979 - Results: Total processed: 11, Charts: 11, Success rate: 90.9%
