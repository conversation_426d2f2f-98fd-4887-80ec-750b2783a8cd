2025-08-18 16:45:22,121 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250818_164522.log
2025-08-18 16:45:22,124 - __main__ - INFO - main:4942 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-18 16:45:22,124 - __main__ - INFO - main:4946 - Start Time: 2025-08-18 16:45:22
2025-08-18 16:45:22,125 - __main__ - INFO - __init__:211 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-18 16:45:22,128 - __main__ - INFO - main:4971 - Valid authentication found, proceeding with processing...
2025-08-18 16:45:22,128 - __main__ - INFO - main:4974 - Starting parallel chart processing workflow with 4 browsers...
2025-08-18 16:45:22,677 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:45:23,015 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:45:40,059 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:45:48,701 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:45:48,998 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:46:03,341 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: 2023-11-01
2025-08-18 16:46:03,358 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: Nov 2023
2025-08-18 16:46:03,373 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: November 2023
2025-08-18 16:46:03,388 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: nov
2025-08-18 16:46:03,403 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 0 (ID: 960) for target: Nov
2025-08-18 16:46:03,403 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: 2023-11-01
2025-08-18 16:46:03,427 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: Nov 2023
2025-08-18 16:46:03,435 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: November 2023
2025-08-18 16:46:03,452 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: nov
2025-08-18 16:46:03,463 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 1 (ID: 944) for target: Nov
2025-08-18 16:46:03,468 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: 2023-11-01
2025-08-18 16:46:03,479 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: Nov 2023
2025-08-18 16:46:03,495 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: November 2023
2025-08-18 16:46:03,503 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: nov
2025-08-18 16:46:03,510 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 2 (ID: 1073) for target: Nov
2025-08-18 16:46:03,526 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: 2023-11-01
2025-08-18 16:46:03,528 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: Nov 2023
2025-08-18 16:46:03,548 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: November 2023
2025-08-18 16:46:03,554 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: nov
2025-08-18 16:46:03,568 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 3 (ID: 1133) for target: Nov
2025-08-18 16:46:03,577 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: 2023-11-01
2025-08-18 16:46:03,588 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: Nov 2023
2025-08-18 16:46:03,600 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: November 2023
2025-08-18 16:46:03,612 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: nov
2025-08-18 16:46:03,618 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 4 (ID: 1127) for target: Nov
2025-08-18 16:46:03,628 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: 2023-11-01
2025-08-18 16:46:03,639 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: Nov 2023
2025-08-18 16:46:03,651 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: November 2023
2025-08-18 16:46:03,654 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: nov
2025-08-18 16:46:03,668 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 5 (ID: 1098) for target: Nov
2025-08-18 16:46:03,685 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: 2023-11-01
2025-08-18 16:46:03,692 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: Nov 2023
2025-08-18 16:46:03,708 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: November 2023
2025-08-18 16:46:03,719 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: nov
2025-08-18 16:46:03,727 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 6 (ID: 1356) for target: Nov
2025-08-18 16:46:03,743 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: 2023-11-01
2025-08-18 16:46:03,748 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: Nov 2023
2025-08-18 16:46:03,760 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: November 2023
2025-08-18 16:46:03,777 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: nov
2025-08-18 16:46:03,779 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 7 (ID: 1044) for target: Nov
2025-08-18 16:46:03,797 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: 2023-11-01
2025-08-18 16:46:03,808 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: Nov 2023
2025-08-18 16:46:03,820 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: November 2023
2025-08-18 16:46:03,827 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: nov
2025-08-18 16:46:03,847 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 8 (ID: 955) for target: Nov
2025-08-18 16:46:03,855 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: 2023-11-01
2025-08-18 16:46:03,869 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: Nov 2023
2025-08-18 16:46:03,869 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: November 2023
2025-08-18 16:46:03,898 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: nov
2025-08-18 16:46:03,909 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 9 (ID: 918) for target: Nov
2025-08-18 16:46:03,916 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: 2023-11-01
2025-08-18 16:46:03,932 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: Nov 2023
2025-08-18 16:46:03,935 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: November 2023
2025-08-18 16:46:03,954 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: nov
2025-08-18 16:46:03,966 - __main__ - INFO - find_matching_points_in_chart:681 - Finding points in chart 10 (ID: 1138) for target: Nov
2025-08-18 16:46:04,828 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:46:04,860 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:46:04,871 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:46:04,916 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:46:05,284 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:46:05,403 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:46:05,421 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:46:05,598 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:46:28,784 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:46:28,882 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:46:29,053 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:46:30,853 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:47:02,521 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:47:02,549 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:47:02,559 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:47:02,575 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:47:03,050 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:47:03,060 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:47:03,073 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:47:03,202 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:47:28,906 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:47:29,273 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:47:29,351 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:47:31,585 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:48:01,558 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:48:01,583 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:48:01,596 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-18 16:48:02,049 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:48:02,064 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:48:02,080 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-18 16:48:27,057 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:48:27,163 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:48:29,555 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-18 16:48:58,219 - __main__ - INFO - main:4978 - Parallel processing with 4 browsers completed successfully!
2025-08-18 16:48:58,219 - __main__ - INFO - main:4979 - Results: Total processed: 11, Charts: 11, Success rate: 90.9%
