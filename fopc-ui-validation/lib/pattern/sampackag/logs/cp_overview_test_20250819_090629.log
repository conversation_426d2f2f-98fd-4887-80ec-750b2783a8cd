2025-08-19 09:06:29,594 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250819_090629.log
2025-08-19 09:06:29,595 - __main__ - INFO - main:5125 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-19 09:06:29,596 - __main__ - INFO - main:5129 - Start Time: 2025-08-19 09:06:29
2025-08-19 09:06:29,596 - __main__ - INFO - __init__:211 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-19 09:06:29,600 - __main__ - INFO - main:5154 - Valid authentication found, proceeding with processing...
2025-08-19 09:06:29,600 - __main__ - INFO - main:5157 - Starting parallel chart processing workflow with 4 browsers...
2025-08-19 09:06:30,068 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-19 09:06:30,389 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-19 09:06:41,395 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-19 09:06:44,071 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: 2023-11-01
2025-08-19 09:06:44,082 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: Nov 2023
2025-08-19 09:06:44,091 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: November 2023
2025-08-19 09:06:44,099 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: nov
2025-08-19 09:06:44,108 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: Nov
