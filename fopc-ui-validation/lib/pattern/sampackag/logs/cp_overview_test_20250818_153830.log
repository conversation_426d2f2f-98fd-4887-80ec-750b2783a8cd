2025-08-18 15:38:30,040 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250818_153830.log
2025-08-18 15:38:30,046 - __main__ - INFO - main:4932 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-18 15:38:30,047 - __main__ - INFO - main:4936 - Start Time: 2025-08-18 15:38:30
2025-08-18 15:38:30,047 - __main__ - INFO - __init__:201 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-18 15:38:30,050 - __main__ - INFO - main:4961 - Valid authentication found, proceeding with processing...
2025-08-18 15:38:30,051 - __main__ - INFO - main:4964 - Starting parallel chart processing workflow with 4 browsers...
2025-08-18 15:38:30,580 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:38:30,887 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:38:47,801 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:38:56,560 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:38:56,856 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:39:11,627 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 0 (ID: 960) for target: 2023-11-01
2025-08-18 15:39:11,645 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 0 (ID: 960) for target: Nov 2023
2025-08-18 15:39:11,666 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 0 (ID: 960) for target: November 2023
2025-08-18 15:39:11,686 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 0 (ID: 960) for target: nov
2025-08-18 15:39:11,703 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 0 (ID: 960) for target: Nov
2025-08-18 15:39:11,726 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 1 (ID: 944) for target: 2023-11-01
2025-08-18 15:39:11,743 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 1 (ID: 944) for target: Nov 2023
2025-08-18 15:39:11,760 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 1 (ID: 944) for target: November 2023
2025-08-18 15:39:11,777 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 1 (ID: 944) for target: nov
2025-08-18 15:39:11,791 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 1 (ID: 944) for target: Nov
2025-08-18 15:39:11,807 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 2 (ID: 1073) for target: 2023-11-01
2025-08-18 15:39:11,820 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 2 (ID: 1073) for target: Nov 2023
2025-08-18 15:39:11,837 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 2 (ID: 1073) for target: November 2023
2025-08-18 15:39:11,853 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 2 (ID: 1073) for target: nov
2025-08-18 15:39:11,870 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 2 (ID: 1073) for target: Nov
2025-08-18 15:39:11,885 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 3 (ID: 1133) for target: 2023-11-01
2025-08-18 15:39:11,902 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 3 (ID: 1133) for target: Nov 2023
2025-08-18 15:39:11,916 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 3 (ID: 1133) for target: November 2023
2025-08-18 15:39:11,936 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 3 (ID: 1133) for target: nov
2025-08-18 15:39:11,950 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 3 (ID: 1133) for target: Nov
2025-08-18 15:39:11,968 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 4 (ID: 1127) for target: 2023-11-01
2025-08-18 15:39:11,981 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 4 (ID: 1127) for target: Nov 2023
2025-08-18 15:39:11,996 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 4 (ID: 1127) for target: November 2023
2025-08-18 15:39:12,012 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 4 (ID: 1127) for target: nov
2025-08-18 15:39:12,026 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 4 (ID: 1127) for target: Nov
2025-08-18 15:39:12,045 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 5 (ID: 1098) for target: 2023-11-01
2025-08-18 15:39:12,058 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 5 (ID: 1098) for target: Nov 2023
2025-08-18 15:39:12,072 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 5 (ID: 1098) for target: November 2023
2025-08-18 15:39:12,089 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 5 (ID: 1098) for target: nov
2025-08-18 15:39:12,104 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 5 (ID: 1098) for target: Nov
2025-08-18 15:39:12,121 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 6 (ID: 1356) for target: 2023-11-01
2025-08-18 15:39:12,134 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 6 (ID: 1356) for target: Nov 2023
2025-08-18 15:39:12,150 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 6 (ID: 1356) for target: November 2023
2025-08-18 15:39:12,164 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 6 (ID: 1356) for target: nov
2025-08-18 15:39:12,182 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 6 (ID: 1356) for target: Nov
2025-08-18 15:39:12,199 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 7 (ID: 1044) for target: 2023-11-01
2025-08-18 15:39:12,215 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 7 (ID: 1044) for target: Nov 2023
2025-08-18 15:39:12,231 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 7 (ID: 1044) for target: November 2023
2025-08-18 15:39:12,247 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 7 (ID: 1044) for target: nov
2025-08-18 15:39:12,264 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 7 (ID: 1044) for target: Nov
2025-08-18 15:39:12,280 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 8 (ID: 955) for target: 2023-11-01
2025-08-18 15:39:12,293 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 8 (ID: 955) for target: Nov 2023
2025-08-18 15:39:12,309 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 8 (ID: 955) for target: November 2023
2025-08-18 15:39:12,326 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 8 (ID: 955) for target: nov
2025-08-18 15:39:12,341 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 8 (ID: 955) for target: Nov
2025-08-18 15:39:12,356 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 9 (ID: 918) for target: 2023-11-01
2025-08-18 15:39:12,371 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 9 (ID: 918) for target: Nov 2023
2025-08-18 15:39:12,390 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 9 (ID: 918) for target: November 2023
2025-08-18 15:39:12,406 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 9 (ID: 918) for target: nov
2025-08-18 15:39:12,421 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 9 (ID: 918) for target: Nov
2025-08-18 15:39:12,437 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 10 (ID: 1138) for target: 2023-11-01
2025-08-18 15:39:12,449 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 10 (ID: 1138) for target: Nov 2023
2025-08-18 15:39:12,464 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 10 (ID: 1138) for target: November 2023
2025-08-18 15:39:12,479 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 10 (ID: 1138) for target: nov
2025-08-18 15:39:12,494 - __main__ - INFO - find_matching_points_in_chart:671 - Finding points in chart 10 (ID: 1138) for target: Nov
2025-08-18 15:39:13,485 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:39:13,513 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:39:13,532 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:39:13,570 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:39:14,031 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:39:14,118 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:39:14,205 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:39:14,392 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:39:46,976 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:39:47,070 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:39:48,772 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:39:50,131 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:40:28,055 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:40:28,078 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:40:28,083 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:40:28,101 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:40:28,825 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:40:29,060 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:40:29,091 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:40:29,136 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:41:08,193 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:41:08,255 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:41:08,305 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:41:10,336 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:41:50,002 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:41:50,017 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:41:50,029 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:41:50,405 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:41:50,500 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:41:50,537 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:42:16,007 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:42:16,069 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:42:18,240 - __main__ - INFO - click_mui_chart_buttons:229 - Ensuring chart containers are visible...
2025-08-18 15:42:51,913 - __main__ - INFO - main:4968 - Parallel processing with 4 browsers completed successfully!
2025-08-18 15:42:51,913 - __main__ - INFO - main:4969 - Results: Total processed: 11, Charts: 11, Success rate: 100.0%
