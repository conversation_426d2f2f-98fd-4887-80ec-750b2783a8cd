2025-08-18 15:21:48,552 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250818_152148.log
2025-08-18 15:21:48,552 - __main__ - INFO - main:5001 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-18 15:21:48,556 - __main__ - INFO - main:5005 - Start Time: 2025-08-18 15:21:48
2025-08-18 15:21:48,556 - __main__ - INFO - __init__:201 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-18 15:21:48,556 - __main__ - INFO - main:5030 - Valid authentication found, proceeding with processing...
2025-08-18 15:21:48,556 - __main__ - INFO - main:5033 - Starting parallel chart processing workflow with 4 browsers...
2025-08-18 15:21:49,083 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:21:49,418 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
2025-08-18 15:21:56,507 - __main__ - INFO - click_mui_chart_buttons:259 - Ensuring chart containers are visible...
2025-08-18 15:22:06,492 - __main__ - INFO - create_authenticated_browser_context:205 - Creating authenticated browser context (headless=False)
2025-08-18 15:22:06,826 - __main__ - INFO - create_authenticated_browser_context:213 - Auth state loaded: Available
