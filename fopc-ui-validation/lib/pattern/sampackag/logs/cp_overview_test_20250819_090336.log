2025-08-19 09:03:36,015 - __main__ - INFO - setup_logging:46 - Logging initialized. Log file: logs\cp_overview_test_20250819_090336.log
2025-08-19 09:03:36,015 - __main__ - INFO - main:5125 - Starting Parallel Chart Processing Application with 4 Browsers
2025-08-19 09:03:36,017 - __main__ - INFO - main:5129 - Start Time: 2025-08-19 09:03:36
2025-08-19 09:03:36,017 - __main__ - INFO - __init__:211 - Initialized MultiChartParallelProcessor with max_browsers=4
2025-08-19 09:03:36,020 - __main__ - INFO - main:5154 - Valid authentication found, proceeding with processing...
2025-08-19 09:03:36,020 - __main__ - INFO - main:5157 - Starting parallel chart processing workflow with 4 browsers...
2025-08-19 09:03:36,502 - __main__ - INFO - create_authenticated_browser_context:215 - Creating authenticated browser context (headless=False)
2025-08-19 09:03:36,926 - __main__ - INFO - create_authenticated_browser_context:223 - Auth state loaded: Available
2025-08-19 09:03:48,073 - __main__ - INFO - click_mui_chart_buttons:239 - Ensuring chart containers are visible...
2025-08-19 09:03:50,737 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: 2023-11-01
2025-08-19 09:03:50,758 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: Nov 2023
2025-08-19 09:03:50,770 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: November 2023
2025-08-19 09:03:50,781 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: nov
2025-08-19 09:03:50,789 - __main__ - INFO - find_matching_points_in_chart:855 - Finding points in chart 0 (ID: 1127) for target: Nov
